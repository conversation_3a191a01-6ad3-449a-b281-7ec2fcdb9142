<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload RusseyKeo Computer Logo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            margin: 20px 0;
        }
        .logo-preview {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }
        .current-logo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 10px;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h1 class="text-center mb-4">🏢 Upload RusseyKeo Computer Logo</h1>
                
                <!-- Current Logo Display -->
                <div class="current-logo text-center mb-4">
                    <h3>Current Logo on Invoices:</h3>
                    <div class="d-flex align-items-center justify-content-center mt-3">
                        <img src="static/icons/logo.png" alt="Current Logo" class="logo-preview me-3" onerror="this.style.display='none'">
                        <div>
                            <h4>RusseyKeo Computer</h4>
                            <p class="mb-0">Professional Computer Sales & Service</p>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="alert alert-info">
                    <h5>📋 Instructions to Update Your Logo:</h5>
                    <ol>
                        <li><strong>Save your logo image</strong> as <code>russeyKeo-logo.png</code></li>
                        <li><strong>Copy the file</strong> to: <code>static/images/brand/russeyKeo-logo.png</code></li>
                        <li><strong>Recommended specifications:</strong>
                            <ul>
                                <li>Format: PNG with transparent background</li>
                                <li>Size: At least 300x300 pixels</li>
                                <li>Aspect ratio: Square or rectangular</li>
                            </ul>
                        </li>
                        <li><strong>Refresh your invoices</strong> to see the new logo</li>
                    </ol>
                </div>

                <!-- Manual Upload Area -->
                <div class="upload-area">
                    <h4>📁 Manual File Upload</h4>
                    <p>Since this is a local development environment, please manually copy your logo file to:</p>
                    <code>static/images/brand/russeyKeo-logo.png</code>
                    <br><br>
                    <div class="alert alert-warning">
                        <strong>Note:</strong> Make sure the filename is exactly <code>russeyKeo-logo.png</code> (case-sensitive)
                    </div>
                </div>

                <!-- Test Links -->
                <div class="text-center">
                    <h5>🧪 Test Your Logo:</h5>
                    <a href="/staff/orders" class="btn btn-primary me-2">View Orders (to test invoice)</a>
                    <a href="/invoice/1" class="btn btn-outline-primary" target="_blank">Test Invoice Preview</a>
                </div>

                <!-- Logo Preview -->
                <div class="mt-4">
                    <h5>🖼️ Logo Preview:</h5>
                    <div class="text-center p-4 border rounded">
                        <img id="logoPreview" src="static/images/brand/russeyKeo-logo.png" alt="Your RusseyKeo Logo" class="logo-preview" 
                             onerror="this.src='static/icons/logo.png'; document.getElementById('logoStatus').innerHTML='❌ Custom logo not found - using fallback';">
                        <p id="logoStatus" class="mt-2 text-success">✅ Custom logo loaded successfully!</p>
                    </div>
                </div>

                <!-- Fallback Information -->
                <div class="alert alert-secondary mt-4">
                    <h6>🔄 Fallback System:</h6>
                    <p class="mb-0">If your custom logo isn't found, the system will automatically use the default logo from <code>static/icons/logo.png</code> to ensure invoices always display properly.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Refresh logo preview every 5 seconds to check for updates
        setInterval(function() {
            const img = document.getElementById('logoPreview');
            const timestamp = new Date().getTime();
            img.src = 'static/images/brand/russeyKeo-logo.png?' + timestamp;
        }, 5000);
    </script>
</body>
</html>
