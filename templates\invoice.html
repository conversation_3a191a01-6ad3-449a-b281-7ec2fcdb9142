<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #{{ order.id }} - RusseyKeo Computer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem; /* Reduced padding for print */
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .invoice-logo {
            height: 60px;
            width: auto;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
        }

        .action-buttons {
            margin: 10px;
        }
        .invoice-details {
            background: #f8f9fa;
            padding: 1rem; /* Reduced padding */
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .invoice-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .total-section {
            background: #e9ecef;
            padding: 0.5rem; /* Reduced padding */
            font-weight: bold;
            font-size: 1rem; /* Slightly smaller font */
        }
        .print-btn {
            background: #28a745;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
        }
        .print-btn:hover {
            background: #218838;
        }
        @media print {
            .no-print { display: none !important; }
            .invoice-header { 
                background: #667eea !important;
                -webkit-print-color-adjust: exact; /* Ensure background prints */
                color-adjust: exact;
            }
            body {
                margin: 0.5cm; /* Reduced margins for more space */
                font-size: 12pt; /* Smaller font size for print */
            }
            .container {
                width: 100% !important;
                max-width: 100% !important;
            }
            .invoice-table, .invoice-details, .total-section, .invoice-header {
                page-break-inside: avoid; /* Prevent breaking these sections */
            }
            .row {
                page-break-inside: avoid; /* Keep rows together */
            }
            @page {
                size: A4; /* Standard page size */
                margin: 0.5cm; /* Minimal margins */
            }
            table {
                font-size: 10pt; /* Smaller table font */
            }
            h1 { font-size: 20pt; } /* Smaller header */
            h4 { font-size: 14pt; }
            p { margin-bottom: 0.3rem; } /* Tighter spacing */
        }
    </style>
</head>
<body>
    <div class="container mt-2">
        <!-- Header -->
        <div class="invoice-header text-center">
            <div class="d-flex align-items-center justify-content-center mb-3">
                <img src="{{ url_for('static', filename='icons/logo.jpg') }}" alt="RusseyKeo Computer Logo" class="invoice-logo me-3">
                <div>
                    <h1 class="mb-1">RusseyKeo Computer</h1>
                    <p class="mb-0">Professional Computer Sales & Service</p>
                </div>
            </div>
        </div>

        <!-- Approval Status Message -->
        {% if order.approval_status == 'Pending Approval' %}
        <div class="alert alert-info text-center mb-3" role="alert">
            <h5 class="alert-heading">📋 Order Confirmation</h5>
            <p class="mb-0">Thank you for your payment! We will approve your order and notify you soon.</p>
        </div>
        {% elif order.approval_status == 'Rejected' %}
        <div class="alert alert-danger text-center mb-3" role="alert">
            <h5 class="alert-heading">❌ Order Rejected</h5>
            <p class="mb-0">Your order has been rejected. Please contact us for more information.</p>
            {% if order.approval_notes %}
            <p class="mb-0"><strong>Reason:</strong> {{ order.approval_notes }}</p>
            {% endif %}
        </div>
        {% endif %}

        <!-- Invoice Details -->
        <div class="row">
            <div class="col-md-6">
                <div class="invoice-details">
                    <h4>📋 Invoice Details</h4>
                    <p><strong>Invoice #:</strong> {{ order.id }}</p>
                    <p><strong>Date:</strong> {{ order.order_date.strftime('%B %d, %Y') }}</p>
                    <p><strong>Time:</strong> {{ order.order_date.strftime('%I:%M %p') }}</p>
                    <p><strong>Payment Method:</strong> {{ order.payment_method or 'ACLEDA Bank QR Payment' }}</p>
                    <p><strong>Payment Status:</strong> <span class="badge bg-success">Paid</span></p>
                    {% if order.approval_status %}
                    <p><strong>Order Status:</strong>
                        {% if order.approval_status == 'Pending Approval' %}
                            <span class="badge bg-warning">Pending Approval</span>
                        {% elif order.approval_status == 'Approved' %}
                            <span class="badge bg-success">Approved</span>
                        {% elif order.approval_status == 'Rejected' %}
                            <span class="badge bg-danger">Rejected</span>
                        {% endif %}
                    </p>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-6">
                <div class="invoice-details">
                    <h4>👤 Customer Information</h4>
                    <p><strong>Name:</strong> {{ customer.first_name }} {{ customer.last_name }}</p>
                    <p><strong>Email:</strong> {{ customer.email }}</p>
                    <p><strong>Phone:</strong> {{ customer.phone or 'N/A' }}</p>
                    <p><strong>Address:</strong> {{ customer.address or 'N/A' }}</p>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <div class="invoice-table">
            <table class="table table-striped mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Product</th>
                        <th>Brand</th>
                        <th>Quantity</th>
                        <th>Original Price</th>
                        <th>Discount</th>
                        <th>Final Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order_items %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            {{ item.product_name }}
                            {% if item.has_discount %}
                            <span class="badge bg-success ms-2">💰 Discounted</span>
                            {% endif %}
                        </td>
                        <td>{{ item.brand }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>
                            {% if item.has_discount %}
                            <span class="text-muted text-decoration-line-through">${{ "%.2f"|format(item.original_price) }}</span>
                            {% else %}
                            ${{ "%.2f"|format(item.original_price) }}
                            {% endif %}
                        </td>
                        <td>
                            {% if item.has_discount %}
                            <span class="text-success fw-bold">
                                -{{ "%.1f"|format(item.discount_percentage) }}%<br>
                                <small>(-${{ "%.2f"|format(item.discount_amount) }})</small>
                            </span>
                            {% else %}
                            <span class="text-muted">No discount</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if item.has_discount %}
                            <span class="text-success fw-bold">${{ "%.2f"|format(item.price) }}</span>
                            {% else %}
                            ${{ "%.2f"|format(item.price) }}
                            {% endif %}
                        </td>
                        <td>
                            <strong>${{ "%.2f"|format(item.quantity * item.price) }}</strong>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Total Section -->
            <div class="total-section text-end">
                <div class="row">
                    <div class="col-md-8"></div>
                    <div class="col-md-4">
                        {% if invoice_summary.has_discounts %}
                        <p class="mb-1">Original Subtotal: <span class="text-muted text-decoration-line-through">${{ "%.2f"|format(invoice_summary.original_total) }}</span></p>

                        {% if invoice_summary.item_discount_total > 0 %}
                        <p class="mb-1 text-success">Item Discounts: -${{ "%.2f"|format(invoice_summary.item_discount_total) }}</p>
                        {% endif %}

                        {% if invoice_summary.has_volume_discount %}
                        <p class="mb-1">Subtotal after item discounts: ${{ "%.2f"|format(invoice_summary.subtotal_before_volume_discount) }}</p>
                        <p class="mb-1 text-success">
                            <i class="fas fa-gift"></i> {{ invoice_summary.volume_discount_rule_name }}:
                            {{ "%.1f"|format(invoice_summary.volume_discount_percentage) }}% off
                            (-${{ "%.2f"|format(invoice_summary.volume_discount_amount) }})
                        </p>
                        {% endif %}

                        <p class="mb-1">Final Subtotal: ${{ "%.2f"|format(order.total_amount) }}</p>
                        {% else %}
                        <p class="mb-1">Subtotal: ${{ "%.2f"|format(order.total_amount) }}</p>
                        {% endif %}
                        <p class="mb-1">Tax: $0.00</p>
                        <hr>
                        <h4>💰 Total Paid: ${{ "%.2f"|format(order.total_amount) }}</h4>
                        {% if invoice_summary.has_discounts %}
                        <p class="text-success mb-0">
                            <small>🎉 You saved ${{ "%.2f"|format(invoice_summary.total_discount) }} on this order!</small>
                            {% if invoice_summary.has_volume_discount %}
                            <br><small>💡 Volume discount applied for orders over ${{ "%.0f"|format(500 if invoice_summary.volume_discount_percentage == 5 else (1000 if invoice_summary.volume_discount_percentage == 10 else 2000)) }}</small>
                            {% endif %}
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Info -->
        <div class="row mt-2">
            <div class="col-md-12">
                <div class="invoice-details text-center">
                    <h5>✅ Payment Confirmed</h5>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-2 no-print action-buttons">
            <button onclick="window.print()" class="btn print-btn me-3">
                🖨️ Print Invoice
            </button>
            <a href="{{ url_for('show_dashboard') }}" class="btn btn-secondary">
                ← Homepage
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>