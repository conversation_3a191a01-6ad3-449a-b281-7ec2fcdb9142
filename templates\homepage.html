<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Computer Shop - Laptops, Desktops, and Accessories</title>
    <link rel="stylesheet" href="/static/css/index.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>


*{
    margin: 0;
    padding: 0;
}
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    
}

header, .top-bar, nav, .hero-container, .categories {
    width: 100%;
    text-align: center;
}

.top-bar {
    background-color: #333;
    color: white;
    padding: 10px 0;
    position: fixed;
    top: 0; /* Ensure it sticks to the top */
    left: 0; /* Align to the left edge */
    width: 100%; /* Full width */
    z-index: 1000; /* Ensure it stays above other content */
    
}

.top-bar span {
    font-size: 2em;
}

nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

nav ul li {
    position: relative;
    margin: 0 15px;
    padding: 10px 0;
}

nav ul li a {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

nav ul li a:hover, nav ul li a:focus {
    background-color: #f0f0f0;
    color: #333;
    outline: none;
}

@keyframes slideDown {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
}

nav ul li .dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #333;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 200px;
    padding: 15px 0;
    animation: slideDown 0.3s ease-in-out;
}

nav ul li .dropdown li {
    margin: 0;
    padding: 0;
}

nav ul li .dropdown li a {
    display: block;
    padding: 12px 20px;
    color: white;
    font-size: 16px;
    transition: background-color 0.3s;
}

nav ul li .dropdown li a:hover, nav ul li .dropdown li a:focus {
    background-color: #444;
    color: #fff;
}

nav ul li:hover .dropdown, nav ul li:focus-within .dropdown {
    display: block;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    color: black;
    
   
}

.search-suggestions {
    position: absolute;
    background-color: #ffffff;
    border: 1px solid #555;
    border-top: none;
    width: 200px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    cursor: pointer;
    border-radius: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    top: 100%;
    left: 30px;
    margin-top: 5px;
}
.search-suggestions div {
    color: #000000;
    background-color: #ffffff;
    padding: 8px 12px;
    border-bottom: 1px solid #333;
    transition: all 0.2s ease;
}
.search-suggestions div:hover {
    background-color: #0c0c0c;
    color: #ffffff;
}

.search-icon {
    cursor: pointer;
    transition: transform 0.3s, filter 0.3s;
}

.search-icon:focus {
    outline: 2px solid #e67e22;
    outline-offset: 2px;
}

.search-icon img:hover, .search-icon img:focus {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.search-input {
    width: 0;
    padding: 0;
    border: none;
    outline: none;
    background-color: white;
    color: black;
    font-size: 16px;
    transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
    position: absolute;
    left: 30px;
    opacity: 0;
    pointer-events: none;
    border-radius: 50px;
}

.search-input:focus {
    border: 1px solid #e67e22;
    outline: none;
}

.search-container.active .search-input {
    width: 200px;
    padding: 5px 10px;
    opacity: 1;
    pointer-events: auto;
    border: 1px solid #e67e22;
}

.hero-container {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    margin: 100px auto 0; /* Consolidated margin: 100px top, auto left/right, 0 bottom */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background-color: #555555;
}

.hero-section {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(to right, gray, #202020);
  
}

.hero-section.active {
    display: flex;
}

.hero-section.animate-in {
    animation: slideIn 1s forwards;
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

.hero-section.animate-in {
    animation: fadeIn 0.5s forwards ease-in-out;
}

.hero-section.animate-out {
    animation: fadeOut 0.5s forwards ease-in-out;
}

.navigation-dots {
    display: flex;
    justify-content: center;
    margin: 10px 0;
}

.dot {
    height: 15px;
    width: 15px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    cursor: pointer;

}

.dot.active {
    background-color: #717171;
}

.hero-image {
    max-width: 40%;
    height: auto;
    margin-right: 20px;
}

.hero-details {
    text-align: left;
}

.hero-section h1 {
    font-size: 2.5em;
    margin: 10px 0;
    color: white;
}

.hero-section .subheading {
    font-size: 1.2em;
    margin: 5px 0;
    color: #ccc;
}

.hero-section h2 {
    font-size: 1.5em;
    color: #e67e22;
    margin: 10px 0;
}

.hero-section button {
    padding: 10px 20px;
    font-size: 1em;
    background-color: #e67e22;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.hero-section button:hover, .hero-section button:focus {
    background-color: #d35400;
    transform: scale(1.05);
    outline: none;
}

.categories {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.category {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 200px;
    padding: 35px;
    border: 3px solid #ccc;
    border-radius: 10px;
    transition: transform 0.3s, background-color 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    
}

.category:hover, .category:focus-within {
    transform: scale(1.05);
    background-color: #f0f0f0;
}

.category-icon {
    max-width: 90px;
    height: auto;
}

.content.blur {
    filter: blur(2px);
    transition: filter 0.3s ease;
}

    .footer1 {
            background-color: #24262b;
            color: white;
            padding: 60px 0;
            margin-top: 40px;
            border-top: 4px solid #e67e22;
        }

        .container1 {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .row1 {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
        }

        .footer1-col {
            flex: 1;
            min-width: 200px;
            padding: 0 15px;
            margin-bottom: 30px;
        }

        .footer1-col h4 {
            font-size: 18px;
            text-transform: capitalize;
            margin-bottom: 25px;
            font-weight: 500;
            position: relative;
        }

        .footer1-col h4::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -10px;
            background-color: #e67e22;
            height: 2px;
            width: 50px;
        }

        .footer1-col ul {
            list-style: none;
            padding: 0;
        }

        .footer1-col ul li {
            margin-bottom: 12px;
        }

        .footer1-col ul li a {
            font-size: 16px;
            text-transform: capitalize;
            color: #bbbbbb;
            text-decoration: none;
            font-weight: 300;
            display: block;
            transition: all 0.3s ease;
        }

        .footer1-col ul li a:hover, .footer1-col ul li a:focus {
            color: #ffffff;
            padding-left: 8px;
        }

        .footer1-col .qr-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .footer1-col .qr-container img {
            width: 170px;
            height: 170px;
            border: 2px solid #e67e22;
            border-radius: 5px;
        }

        .footer1-col .address-text {
            font-size: 14px;
            color: #bbbbbb;
            line-height: 1.6;
        }

.card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 200px;
    object-fit: contain;
    padding: 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.card-body {
    display: flex;
    flex-direction: column;
}

.card-text {
    flex-grow: 1;
    color: #6c757d;
}

.price {
    color: #0d6efd;
    font-size: 1.1rem;
    font-weight: bold;
}

.add-to-cart {
    transition: all 0.3s;
}

.add-to-cart:hover {
    background-color: #0b5ed7 !important;
    color: white !important;
}

/* View More Button Styling for All Categories */
[id$="-view-more-container"] {
    transition: opacity 0.3s ease-in-out;
}

[id$="-view-more-btn"] {
    padding: 12px 30px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

[id$="-view-more-btn"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Smooth transition for product grid changes */
[id$="-products-container"] {
    transition: all 0.3s ease-in-out;
}

    .container {
    width: 60% !important;
    max-width: none !important; /* Removes Bootstrap's max-width constraints */
    margin: auto;
}

    .container {
        width: 60% !important;
        max-width: none !important; /* Removes Bootstrap's max-width constraints */
        margin: auto;
    }

    /* Enhanced Navigation Styling */
    .nav-item .nav-link {
        transition: all 0.3s ease;
        border-radius: 5px;
        margin: 0 5px;
    }

    .nav-item .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
    }

    .dropdown-menu {
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        margin-top: 5px;
    }

    .dropdown-item {
        transition: all 0.3s ease;
        border-radius: 5px;
        margin: 2px 5px;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }

    .dropdown-item.text-danger:hover {
        background-color: #f8d7da;
        color: #721c24 !important;
    }

    .btn-outline-light {
        transition: all 0.3s ease;
        border-radius: 20px;
        padding: 8px 16px;
    }

    .btn-outline-light:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* User icon styling */
    .bi-person-circle {
        font-size: 1.2em !important;
    }

    /* Username text styling */
    .username-text {
        font-size: 0.95rem !important;
        font-weight: 400;
    }

    /* Notification styles */
    .notification-badge {
        background-color: #dc3545;
        color: white;
        border-radius: 50%;
        padding: 0px 2px;
        font-size: 0.4rem;
        margin-left: 3px;
        vertical-align: middle;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 12px;
        height: 12px;
    }

    .notification-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .notification-content {
        background-color: white;
        margin: 5% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        max-height: 80%;
        overflow-y: auto;
    }

    .notification-item {
        padding: 15px;
        border-bottom: 1px solid #eee;
        margin-bottom: 10px;
    }

    .notification-item.unread {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
    }

    .notification-item.order_cancelled {
        border-left: 4px solid #dc3545;
    }

    .notification-item.recent {
        border: 1px solid #28a745;
        background-color: #f8fff9;
    }

    .notification-date {
        color: #666;
        font-size: 12px;
    }

    .close-modal {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .close-modal:hover {
        color: black;
    }



    .discount-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9em;
        box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        z-index: 10;
    }

    .price-container {
        margin: 15px 0;
    }

    .original-price {
        text-decoration: line-through;
        color: #999;
        font-size: 1em;
        margin-right: 10px;
    }

    .sale-price {
        color: #e74c3c;
        font-size: 1.3em;
        font-weight: bold;
    }

    .savings-text {
        color: #27ae60;
        font-size: 0.9em;
        font-weight: bold;
        margin-top: 5px;
    }

    .discount-card {
        position: relative;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .discount-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        /* border-color: #e74c3c; */
    }

    .discount-card .card-img-top {
        transition: transform 0.3s ease;
    }

    .discount-card:hover .card-img-top {
        transform: scale(1.05);
    }

    #discount-view-more-btn {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        border: none;
        color: white;
        padding: 15px 30px;
        font-size: 1.1em;
        font-weight: bold;
        border-radius: 25px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    }

    #discount-view-more-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        background: linear-gradient(45deg, #c0392b, #a93226);
    }

    /* Discount styles for regular product cards */
    /* .product-card.discount-card {
        border: 2px solid #e74c3c;
    } */

    .product-card .discount-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        padding: 6px 10px;
        border-radius: 15px;
        font-weight: bold;
        font-size: 0.8em;
        box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        z-index: 10;
    }

    .product-card .price-container .original-price {
        text-decoration: line-through;
        color: #999;
        font-size: 0.9em;
        margin-right: 8px;
    }

    .product-card .price-container .sale-price {
        color: #e74c3c;
        font-size: 1.2em;
        font-weight: bold;
    }

    .product-card .price-container .savings-text {
        color: #27ae60;
        font-size: 0.8em;
        font-weight: bold;
        margin-top: 3px;
    }

    /* Limit product names to 1 line only */
    .card-title {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
        max-height: 1.4em; /* 1 line × 1.4 line-height */
        word-wrap: break-word;
        -webkit-hyphens: auto;
        hyphens: auto;
        white-space: nowrap;
    }

    /* Ensure consistent card heights with 2-line titles */
    .card-body {
        display: flex;
        flex-direction: column;
        min-height: 280px;
    }

    .card-body .card-text {
        flex-grow: 1;
        margin-top: auto;
        margin-bottom: 15px;
    }

    .card-body .d-flex.gap-2 {
        margin-top: auto;
    }
    
 .top-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 10px 0;
}

.logo-left {
    position: absolute;
    left: 20px;
    top: 90%;
    transform: translateY(-50%);
}

.logo {
    height: 110px;
    width: auto;
    border-radius: 8px;
    margin-right: 15px;
}


.site-title {
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-container {
    height: 400px;
    margin-top: 130px; /* To avoid overlap with fixed top-bar */
  }
  .hero-image {
    max-width: 50%;
    margin: 0 auto;
  }
  .category {
    width: 120px;
    flex: 1 1 auto; /* allow flex wrap */
  }
  nav ul {
    flex-direction: column;
    align-items: center; /* center nav items on smaller screens */
    padding-left: 0;
  }
  nav ul li {
    margin: 8px 0;
    width: 100%;
    text-align: left;
  }
  nav ul li a {
    display: block;
    width: 100%;
  }
  .search-container.active .search-input {
    width: 90%;
    left: 5%;
    position: relative; /* to fit inside container */
  }
  .footer1-col {
    width: 50%;
    margin-bottom: 30px;
    text-align: center;
  }
  .logo-left {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
  }
  .logo {
    height: 80px;
  }
}

@media (max-width: 576px) {
  .hero-container {
    height: 300px;
    margin-top: 130px;
  }
  .hero-section h1 {
    font-size: 1em;
  }
  .hero-section .subheading {
    font-size: 1em;
  }
  .hero-section h2 {
    font-size: 1.2em;
  }
  .category {
    width: 100px;
    flex: 1 1 auto;
  }
  .footer1-col {
    width: 100%;
    text-align: center;
  }
  .search-container.active .search-input {
    width: 95%;
    left: 2.5%;
    position: relative;
  }
  .logo {
    height: 60px;
  }
  nav ul li {
    margin: 6px 0;
  }
}

/* Hamburger button hidden on desktop, visible on mobile */
.nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  margin-left: auto;
  padding: 5px 10px;
  user-select: none;
}

@media (max-width: 768px) {
  .nav-toggle {
    display: block;
  }

  /* Hide nav menu by default on mobile */
  nav {
    display: none;
    width: 100%;
    background-color: #333;
  }

  nav.nav-open {
    display: block;
  }

  nav ul {
    flex-direction: column;
    align-items: flex-start;
    padding-left: 0;
    margin: 0;
  }

  nav ul li {
    width: 100%;
    margin: 10px 0;
  }

  nav ul li a {
    display: block;
    width: 100%;
  }

  /* Dropdown menus become static and full width */
  nav ul li .dropdown {
    position: static;
    box-shadow: none;
    border-radius: 0;
    min-width: 100%;
    padding-left: 15px;
  }

  /* Adjust top-header */
  .top-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    position: relative;
  }

 .logo-left {
    position: relative;
    margin: 0 auto;
    left: 0;
    transform: none;
  }
  .logo {
    height: 60px;
  }
}

/* Search input toggle */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  color: black;
}

.search-input {
  width: 0;
  padding: 0;
  border: none;
  outline: none;
  background-color: white;
  color: black;
  font-size: 16px;
  transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
  position: absolute;
  left: 30px;
  opacity: 0;
  pointer-events: none;
  border-radius: 50px;
}

.search-container.active .search-input {
  width: 200px;
  padding: 5px 10px;
  opacity: 1;
  pointer-events: auto;
  border: 1px solid #e67e22;
}

@media (max-width: 576px) {
  .search-container.active .search-input {
    width: 90vw;
    left: 5vw;
    position: fixed;
    top: 60px;
    z-index: 1500;
    
  }
}

/* Updated Responsive Footer */
@media (max-width: 992px) {
  .row1 {
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }

  .footer1-col {
    width: 80%;
    text-align: center;
  }

  .footer1-col .qr-container {
    flex-direction: column;
    justify-content: center;
  }

  .footer1-col .qr-container img {
    width: 150px;
    height: 150px;
  }

  .footer1-col .address-text {
    text-align: center;
  }
}

@media (max-width: 576px) {
  .footer1 {
    padding: 40px 0;
  }

  .footer1-col {
    width: 100%;
    padding: 0 10px;
  }

  .footer1-col h4::before {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer1-col ul li a {
    font-size: 14px;
  }

  .footer1-col .qr-container img {
    width: 130px;
    height: 130px;
  }

  .footer1-col .address-text {
    font-size: 13px;
  }
}

    </style>
</head>
<body>

 <header>
  <div class="top-bar">
    <div class="top-header">
      <div class="logo-left">
        <img src="/static/icons/logo.jpg" alt="Company Logo" class="logo" />
      </div>
      <div class="site-title">Russeykeo Computer</div>
      <!-- Hamburger button visible only on mobile -->
      <button
        class="nav-toggle"
        aria-label="Toggle navigation menu"
        aria-expanded="false"
      >
        &#9776;
      </button>
    </div>
    <nav>
      <ul>
        <li><a href="{{ url_for('show_dashboard') }}">Home</a></li>

        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">Categories</a>
          <ul class="dropdown">
            <li><a href="/products/category/multi/1,5">Laptops</a></li>
            <li><a href="/products/category/2">Desktops</a></li>
            <li><a href="/products/category/3">Accessories</a></li>
          </ul>
        </li>
        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">Products</a>
          <ul class="dropdown">
            <li><a href="/products/brand/dell">Dell</a></li>
            <li><a href="/products/brand/hp">HP</a></li>
            <li><a href="/products/brand/lenovo">Lenovo</a></li>
            <li><a href="/products/brand/asus">Asus</a></li>
            <li><a href="/products/brand/acer">Acer</a></li>
            <li><a href="/products/brand/razer">Razer</a></li>
          </ul>
        </li>
        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">About Company</a>
          <ul class="dropdown">
            <li><a href="{{ url_for('about') }}">About Us</a></li>
            <!-- <li><a href="{{ url_for('services') }}">Our Services</a></li>
            <li><a href="{{ url_for('privacy') }}">Privacy Policy</a></li> -->
          </ul>
        </li>
        <li><a href="{{ url_for('cart') }}">My Cart</a></li>
        {% if not session.username %}
        <li><a href="{{ url_for('auth.register') }}">Create Account</a></li>
        {% endif %}
        {% if session.username %}
          {% if session.role == 'customer' %}
          <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
          <li>
            <a href="#" onclick="showNotifications()" id="notifications-link">
              Notifications
              <span
                id="notification-badge"
                class="notification-badge"
                style="display: none;"
                >0</span
              >
            </a>
          </li>
          {% elif session.role in ['staff', 'admin', 'super_admin'] %}
          <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
          <li>
            <a href="#" onclick="showNotifications()" id="notifications-link">
              Notifications
              <span
                id="notification-badge"
                class="notification-badge"
                style="display: none;"
                >0</span
              >
            </a>
          </li>
          {% endif %}
        {% endif %}
        {% if session.username %}
        <li class="nav-item dropdown">
          <a
            class="nav-link dropdown-toggle d-flex align-items-center text-white"
            href="#"
            id="userDropdown"
            role="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <i class="bi bi-person-circle me-2"></i>
            <span class="username-text">{{ session.username }}</span>
          </a>
          <ul
            class="dropdown-menu dropdown-menu-end"
            aria-labelledby="userDropdown"
          >
            <li><hr class="dropdown-divider" /></li>
            <li>
              <a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}"
                ><i class="bi bi-box-arrow-right me-2"></i>Logout</a
              >
            </li>
          </ul>
        </li>
        {% else %}
        <li class="nav-item">
          <a
            href="/auth/login"
            class="btn btn-outline-light btn-sm d-flex align-items-center"
          >
            <i class="bi bi-person-plus me-2"></i> Login Account
          </a>
        </li>
        {% endif %}
        <li>
          <form
            class="search-container"
            action="/search"
            method="GET"
            onsubmit="return validateSearch()"
          >
            <button
              type="button"
              class="search-icon"
              tabindex="0"
              aria-label="Search products"
              style="background:none; border:none; padding:0; cursor:pointer;"
            >
              <img
                src="/static/icons/search.png"
                alt="Search Icon"
                style="height: 20px"
              />
            </button>
            <input
              type="text"
              name="q"
              class="search-input"
              placeholder="Search..."
              aria-label="Search products"
              required
            />
            <div class="search-suggestions"></div>
          </form>
        </li>
      </ul>
    </nav>
  </div>
</header>


    <div class="content"> 
        <div class="hero-container">
            <div class="hero-section active">
                <img src="/static/icons/product/macbook.png" alt="MacBook Pro with Retina display" class="hero-image">
                <div class="hero-details">
                    <h1>MacBook Pro</h1>
                    <p class="subheading">with Retina display</p>
                    <h2>$1999</h2>
                </div>
            </div>
            <div class="hero-section">
                <img src="/static/icons/product/asus zenbook.png" alt="Asus Zenbook with OLED display" class="hero-image">
                <div class="hero-details">
                    <h1>Asus Zenbook</h1>
                    <p class="subheading">with OLED display</p>
                    <h2>$2200</h2>
                </div>
            </div>
            <div class="hero-section">
                <img src="/static/icons/product/razer blackwidow.png" alt="Razer Blackwidow Chroma Red Switch Keyboard" class="hero-image">
                <div class="hero-details">
                    <h1>Razer Blackwidow Chroma</h1>
                    <p class="subheading">Red Switch Keyboard</p>
                    <h2>$199</h2>
                </div>
            </div>
            <div class="hero-section">
                <img src="/static/icons/product/asus rog swift.png" alt="Asus Rog Swift Nvidia G-Sync Monitor" class="hero-image">
                <div class="hero-details">
                    <h1>Asus Rog Swift</h1>
                    <p class="subheading">Nvidia G-Sync 21:9 aspect ratio Computer Monitor</p>
                    <h2>$399</h2>
                </div>
            </div>
        </div>
        <div class="navigation-dots">
            <span class="dot active" data-index="0"></span>
            <span class="dot" data-index="1"></span>
            <span class="dot" data-index="2"></span>
            <span class="dot" data-index="3"></span>
        </div>

        <div class="categories" >
             <div class="categories">
                <div class="category">
                    <img src="/static/icons/msi.png" alt="Accessories Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/ASUS.png" alt="Laptops Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/Lanovo.png" alt="Desktops Icon" class="category-icon">
                </div>
                
                 <div class="category">
                    <img src="/static/icons/Acer.png" alt="PC Components Icon" class="category-icon">
                </div>
                 <div class="category">
                    <img src="/static/icons/Dell.png" alt="PC Components Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/Razer.png" alt="PC Components Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/HP.png" alt="PC Components Icon" class="category-icon">
                </div>
                
            </div>
        </div>



  <div class="custom-container" style="margin: 50px;">
    <div class="row g-4">
        <!-- Categories Sidebar -->
        <div class="col-lg-2 col-md-3 col-12" style="margin: 120px 0px 0px 0px;">
 <a href="/products/category/multi/1,5" class="category-link" style="text-decoration: none; color: inherit;">
                <div class="category" style="margin: 10px;">
                    <img src="/static/icons/laptop.png" alt="Laptops Icon" class="category-icon" style="width: 40px; height: 40px;">
                    <h4>Laptops</h4>
                </div>
            </a>
            <a href="/products/category/2" class="category-link" style="text-decoration: none; color: inherit;">
                <div class="category" style="margin: 10px;">
                    <img src="/static/icons/desktop.png" alt="Desktops Icon" class="category-icon" style="width: 40px; height: 40px;">
                    <h4>Desktops</h4>
                </div>
            </a>
            <a href="/products/category/3" class="category-link" style="text-decoration: none; color: inherit;">
                <div class="category" style="margin: 10px;">
                    <img src="/static/icons/keyboard.png" alt="Accessories Icon" class="category-icon" style="width: 40px; height: 40px;">
                    <h4>Accessories</h4>
                </div>
            </a>
        </div>
        <!-- Hot Deals & Discounts Products -->
        <div class="col-lg-10 col-md-9 col-12">
            <section id="products">
                <div class="d-flex justify-content-between align-items-center mb-5">
                    <h2 style="color: #dc3545;"><i class="fas fa-fire"></i>Discounts Products</h2>
                    <!-- <a href="/products/all" class="btn btn-outline-primary">View All</a> -->
                </div>
                <div id="discount-products-container" class="row g-4">
                </div>
                <!-- View More Button for Discounts -->
                <div id="discount-view-more-container" class="text-center mt-4" style="display: none;">
                    <button id="discount-view-more-btn" class="btn btn-outline-primary">
                        View More Deals
                    </button>
                </div>
            </section>
        </div>
    </div>
</div>


  <div class="custom-container" style="margin: 50px;">
    <div class="row g-4">
        <!-- Categories Sidebar -->
        <div class="col-lg-2 col-md-3 col-12" style="margin: 120px 0px 0px 0px;">
            
           
            
        </div>
        <!-- Laptops Products -->
        <div class="col-lg-10 col-md-9 col-12">
            <section id="products">
                <div class="d-flex justify-content-between align-items-center mb-5">
                    <h2>Laptops Products</h2>
                    <!-- <a href="/products/all" class="btn btn-outline-primary">View All</a> -->
                </div>
                <div id="laptops-products-container" class="row g-4">
                </div>
                <!-- View More Button for Laptops -->
                <div id="laptops-view-more-container" class="text-center mt-4" style="display: none;">
                    <button id="laptops-view-more-btn" class="btn btn-outline-primary">
                        View More Laptops
                    </button>
                </div>
            </section>
        </div>
    </div>
</div>


  <div class="custom-container" style="margin: 50px;">
    <div class="row g-4">
        <!-- Categories Sidebar -->
        <div class="col-lg-2 col-md-3 col-12" style="margin: 120px 0px 0px 0px;">
            
            <!-- <div class="category"style="margin: 10px;">
                <img src="/static/icons/laptop.png" alt="Laptops Icon" class="category-icon" style="width: 40px; height: 40px;">
                <h4>Laptops</h4>
            </div>
            <div class="category"style="margin: 10px;">
                <img src="/static/icons/desktop.png" alt="Desktops Icon" class="category-icon" style="width: 40px; height: 40px;">
                <h4>Desktops</h4>
            </div>
            <div class="category" style="margin: 10px;">
                <img src="/static/icons/keyboard.png" alt="Accessories Icon" class="category-icon" style="width: 40px; height: 40px;">
                <h4>Accessories</h4>
            </div> -->
          
        </div>
        <!-- Desktops Products -->
        <div class="col-lg-10 col-md-9 col-12">
            <section id="products">
                <div class="d-flex justify-content-between align-items-center mb-5">
                    <h2>Desktops Products</h2>
                    <!-- <a href="/products/all" class="btn btn-outline-primary">View All</a> -->
                </div>
                <div id="desktops-products-container" class="row g-4">
                </div>
                <!-- View More Button for Desktops -->
                <div id="desktops-view-more-container" class="text-center mt-4" style="display: none;">
                    <button id="desktops-view-more-btn" class="btn btn-outline-primary">
                        View More Desktops
                    </button>
                </div>
            </section>
        </div>
    </div>
</div>

  <div class="custom-container" style="margin: 50px;">
    <div class="row g-4">
        <!-- Categories Sidebar -->
        <div class="col-lg-2 col-md-3 col-12" style="margin: 120px 0px 0px 0px;">
            
            <!-- <div class="category"style="margin: 10px;">
                <img src="/static/icons/laptop.png" alt="Laptops Icon" class="category-icon" style="width: 40px; height: 40px;">
                <h4>Laptops</h4>
            </div>
            <div class="category"style="margin: 10px;">
                <img src="/static/icons/desktop.png" alt="Desktops Icon" class="category-icon" style="width: 40px; height: 40px;">
                <h4>Desktops</h4>
            </div>
            <div class="category" style="margin: 10px;">
                <img src="/static/icons/keyboard.png" alt="Accessories Icon" class="category-icon" style="width: 40px; height: 40px;">
                <h4>Accessories</h4>
            </div> -->
          
        </div>
        <!-- Accessories Products -->
        <div class="col-lg-10 col-md-9 col-12">
            <section id="products">
                <div class="d-flex justify-content-between align-items-center mb-5">
                    <h2>Accessories Products</h2>
                    <!-- <a href="/products/all" class="btn btn-outline-primary">View All</a> -->
                </div>
                <div id="accessories-products-container" class="row g-4">
                </div>
                <!-- View More Button for Accessories -->
                <div id="accessories-view-more-container" class="text-center mt-4" style="display: none;">
                    <button id="accessories-view-more-btn" class="btn btn-outline-primary">
                        View More Accessories
                    </button>
                </div>
            </section>
        </div>
    </div>
</div>

    <footer class="footer1">
        <div class="container1">
            <div class="row1">
                <div class="footer1-col">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="{{ url_for('about') }}">About Us</a></li>
                        <!-- <li><a href="{{ url_for('services') }}">Our Services</a></li>
                        <li><a href="{{ url_for('privacy') }}">Privacy Policy</a></li> -->
                       
                    </ul>
                </div>
                <div class="footer1-col">
                    <h4>Online Shop</h4>
                    <ul>
                        <li><a href="/products/category/multi/1,5">Laptops</a></li>
                        <li><a href="/products/category/2">Desktops</a></li>
                        <li><a href="/products/category/3">Accessories</a></li>
                    </ul>
                </div>
               <div class="footer1-col">
                    <h4>Shop Address</h4>
                    <div class="qr-container">
                            <img src="/static/icons/QR1.png" alt="Shop QR Code">
                    </div>
                    <div class="address-text">
                    <a href="https://maps.app.goo.gl/asmvfLTkajoffidN6?g_st=com.google.maps.preview.copy" 
                    target="_blank" 
                    style="text-decoration: none; color: #007BFF;" 
                    onmouseover="this.style.color='#0056b3'" 
                    onmouseout="this.style.color='#007BFF'">
                        No. 829B, entrance to Russey Keo High School, <br>
                        Sangkat Russey Keo, Khan Russey Keo, <br>
                        Phnom Penh.
                    </a>
                </div>
                </div>
            </div>
        </div>
    </footer>
    <script src="/static/js/unified-notifications.js"></script>
    <script src="/static/js/homepage.js"></script>
    <script src="/static/js/preorder_state_manager.js"></script>
    <script src="/static/js/homepage_products_v2.js"></script>
    <script src="/static/js/homepage_discounts.js"></script>
    <script src="/static/js/view_product_button.js"></script>

    <script>
        // Additional fix for My Pre-Orders navigation
        document.addEventListener('DOMContentLoaded', function() {
            const preOrdersLinks = document.querySelectorAll('a[href*="customer_preorders"]');
            preOrdersLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('Pre-orders link clicked from homepage');
                    // Ensure navigation works
                    if (!e.defaultPrevented) {
                        window.location.href = this.href;
                    }
                });
            });
        });
    </script>
    <script src="/static/js/category_navigation.js"></script>
    <script src="/static/js/brand_navigation.js"></script>
 
    <!-- Bootstrap JavaScript for dropdown functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Notification Modal -->
    <div id="notificationModal" class="notification-modal">
        <div class="notification-content">
            <span class="close-modal" onclick="closeNotifications()">&times;</span>
            <h2>Notifications</h2>
            <div id="notificationsList">
                <p>Loading notifications...</p>
            </div>
            <div style="margin-top: 20px;">
                <button onclick="markAllAsRead()" class="btn btn-primary">Mark All as Read</button>
                <button onclick="clearAllNotifications()" class="btn btn-warning">Clear All</button>
                <button onclick="closeNotifications()" class="btn btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <script>
        function validateSearch() {
            const input = document.querySelector('.search-input');
            if (!input.value.trim()) {
                window.unifiedNotifications.warning('Please enter a search term.');
                return false;
            }
            return true;
        }

        // Notification system
        let notifications = [];

        // Load notifications when page loads (for logged-in customers)
    </script>

    {% if session.username and session.role == 'customer' %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            // Check for new notifications every 30 seconds
            setInterval(loadNotifications, 30000);
        });
    </script>
    {% endif %}

    <script>

        function loadNotifications() {
            return fetch('/api/notifications')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        notifications = data.notifications;
                        updateNotificationBadge();

                        // Show custom notification for new unread notifications (only once per session)
                        const unreadNotifications = notifications.filter(n => !n.is_read);
                        if (unreadNotifications.length > 0) {
                            const newNotifications = unreadNotifications.filter(n => {
                                return !sessionStorage.getItem(`notification_shown_${n.id}`);
                            });

                            if (newNotifications.length > 0) {
                                // Mark as shown in session storage
                                newNotifications.forEach(n => {
                                    sessionStorage.setItem(`notification_shown_${n.id}`, 'true');
                                });

                                // Show custom notification popup (no browser permission needed)
                                const latestNotification = newNotifications[0];
                                setTimeout(() => {
                                    showCustomNotificationPopup(latestNotification.message);
                                }, 1000);
                            }
                        }
                    }
                    return data; // Return the data for chaining
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    throw error; // Re-throw for proper error handling
                });
        }

        function updateNotificationBadge() {
            const badge = document.getElementById('notification-badge');
            const unreadCount = notifications.filter(n => !n.is_read).length;

            // Show badge only for unread notifications
            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.style.display = 'inline-flex';
                badge.style.backgroundColor = '#dc3545'; // Red for unread
                // Set balanced size styles
                badge.style.fontSize = '0.75rem';
                badge.style.minWidth = '20px';
                badge.style.height = '20px';
                badge.style.padding = '2px 6px';
                badge.style.marginLeft = '5px';
            } else {
                // Hide badge completely when all notifications are read
                badge.style.display = 'none';
            }
        }

        function showNotifications() {
            const modal = document.getElementById('notificationModal');
            const list = document.getElementById('notificationsList');

            if (notifications.length === 0) {
                list.innerHTML = '<p>No notifications</p>';
            } else {
                let html = '';
                const now = new Date();
                const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

                notifications.forEach(notification => {
                    const readClass = notification.is_read ? '' : 'unread';
                    const typeClass = notification.type || '';
                    const notificationDate = new Date(notification.created_at || notification.created_date);
                    const isRecent = notificationDate > oneDayAgo;
                    const recentClass = isRecent ? 'recent' : '';

                    html += `
                        <div class="notification-item ${readClass} ${typeClass} ${recentClass}" data-id="${notification.id}">
                            <div>${notification.message} ${isRecent && notification.is_read ? '<span style="color: #28a745; font-size: 12px;">(Recent)</span>' : ''}</div>
                            <div class="notification-date">${notificationDate.toLocaleString()}</div>
                            ${!notification.is_read ? '<button onclick="markAsRead(' + notification.id + ')" class="btn btn-sm btn-outline-primary" style="margin-top: 5px;">Mark as Read</button>' : ''}
                        </div>
                    `;
                });
                list.innerHTML = html;
            }

            modal.style.display = 'block';
        }

        function closeNotifications() {
            document.getElementById('notificationModal').style.display = 'none';
        }

        function markAsRead(notificationId) {
            // Disable the button immediately to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Marking...';

            fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Only refresh the notifications data and modal once
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
                // Re-enable button on error
                button.disabled = false;
                button.textContent = 'Mark as Read';
            });
        }

        function markAllAsRead() {
            // Disable the button to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Marking All...';

            fetch('/api/customer/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Only refresh once with proper chaining
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
                // Re-enable button on error
                button.disabled = false;
                button.textContent = 'Mark All as Read';
            });
        }

        async function clearAllNotifications() {
            // Show custom confirmation modal
            const confirmed = await showClearNotificationsConfirmation();
            if (!confirmed) {
                return;
            }

            // Disable the button to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Clearing All...';

            fetch('/api/customer/notifications/clear-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh notifications and update badge
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                } else {
                    window.unifiedNotifications.error('Error clearing notifications: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error clearing all notifications:', error);
                window.unifiedNotifications.error('An error occurred while clearing notifications.');
            })
            .finally(() => {
                // Re-enable button
                button.disabled = false;
                button.textContent = 'Clear All';
            });
        }

        function showClearNotificationsConfirmation() {
            return new Promise((resolve) => {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.className = 'clear-notifications-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    backdrop-filter: blur(2px);
                `;

                // Create modal
                const modal = document.createElement('div');
                modal.style.cssText = `
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                    max-width: 400px;
                    width: 90%;
                    transform: scale(0.9);
                    transition: transform 0.2s ease-out;
                `;

                modal.innerHTML = `
                    <div style="padding: 24px 24px 16px 24px; text-align: center;">
                        <div style="color: #f59e0b; font-size: 48px; margin-bottom: 16px;">🗑️</div>
                        <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600; color: #111827;">Clear All Notifications</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px;">Are you sure you want to clear all notifications? This action cannot be undone.</p>
                    </div>
                    <div style="padding: 16px 24px 24px 24px; display: flex; gap: 12px; justify-content: center;">
                        <button class="cancel-btn" style="
                            background: #f3f4f6;
                            color: #374151;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Cancel</button>
                        <button class="confirm-btn" style="
                            background: #f59e0b;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Clear All</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                // Animate in
                requestAnimationFrame(() => {
                    modal.style.transform = 'scale(1)';
                });

                // Add hover effects
                const cancelBtn = modal.querySelector('.cancel-btn');
                const confirmBtn = modal.querySelector('.confirm-btn');

                cancelBtn.addEventListener('mouseenter', () => {
                    cancelBtn.style.backgroundColor = '#e5e7eb';
                });
                cancelBtn.addEventListener('mouseleave', () => {
                    cancelBtn.style.backgroundColor = '#f3f4f6';
                });

                confirmBtn.addEventListener('mouseenter', () => {
                    confirmBtn.style.backgroundColor = '#d97706';
                });
                confirmBtn.addEventListener('mouseleave', () => {
                    confirmBtn.style.backgroundColor = '#f59e0b';
                });

                // Handle button clicks
                const cleanup = () => {
                    modal.style.transform = 'scale(0.9)';
                    overlay.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 200);
                };

                cancelBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(false);
                });

                confirmBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(true);
                });

                // Close on overlay click
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        cleanup();
                        resolve(false);
                    }
                });

                // Close on escape key
                const handleEscape = (e) => {
                    if (e.key === 'Escape') {
                        cleanup();
                        resolve(false);
                        document.removeEventListener('keydown', handleEscape);
                    }
                };
                document.addEventListener('keydown', handleEscape);
            });
        }

        function showCustomNotificationPopup(message) {
            // Create custom notification popup (no browser permission needed)
            const popup = document.createElement('div');
            popup.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #007bff;
                color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 350px;
                font-family: Arial, sans-serif;
                border-left: 4px solid #0056b3;
            `;

            popup.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div>
                        <div style="font-weight: bold; margin-bottom: 8px;">New Notification</div>
                        <div style="font-size: 14px; line-height: 1.4;">${message}</div>
                        <div style="margin-top: 12px;">
                            <button onclick="showNotifications(); this.parentElement.parentElement.parentElement.remove();" style="background: white; color: #007bff; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 8px;">View All</button>
                            <button onclick="this.parentElement.parentElement.parentElement.remove();" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">Dismiss</button>
                        </div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove();" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; margin-left: 10px;">&times;</button>
                </div>
            `;

            document.body.appendChild(popup);

            // Auto-remove after 8 seconds
            setTimeout(() => {
                if (popup.parentNode) {
                    popup.remove();
                }
            }, 8000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('notificationModal');
            if (event.target == modal) {
                closeNotifications();
            }
        }


document.addEventListener('DOMContentLoaded', () => {
  const navToggle = document.querySelector('.nav-toggle');
  const nav = document.querySelector('nav');
  const searchContainer = document.querySelector('.search-container');
  const searchIcon = document.querySelector('.search-icon');
  const searchInput = document.querySelector('.search-input');

  // Toggle nav menu on hamburger click
  navToggle.addEventListener('click', () => {
    const expanded = nav.classList.toggle('nav-open');
    navToggle.setAttribute('aria-expanded', expanded);
  });

  // Toggle search input on search icon click
  searchIcon.addEventListener('click', () => {
    searchContainer.classList.toggle('active');
    if (searchContainer.classList.contains('active')) {
      searchInput.focus();
    } else {
      searchInput.value = '';
    }
  });

  // Close nav or search if clicked outside
  document.addEventListener('click', (e) => {
    if (
      !nav.contains(e.target) &&
      !navToggle.contains(e.target)
    ) {
      nav.classList.remove('nav-open');
      navToggle.setAttribute('aria-expanded', false);
    }
    if (
      !searchContainer.contains(e.target)
    ) {
      searchContainer.classList.remove('active');
      searchInput.value = '';
    }
  });
});

    </script>
</body>
</html>
