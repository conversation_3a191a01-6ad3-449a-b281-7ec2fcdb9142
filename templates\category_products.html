<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{% if brand %}{{ brand.upper() }} Products{% elif category %}{% if category.name.lower() == 'laptops' or category.name.lower() == 'laptop' or category.name.lower() == 'laptop_gaming' %}Laptops{% elif category.name.lower() == 'desktops' or category.name.lower() == 'desktop' %}Desktops{% elif category.name.lower() == 'accessories' %}Accessories{% else %}{{ category.name.title() }}{% endif %}{% else %}Products in Category{% endif %}</title>
    <link rel="stylesheet" href="/static/css/index.css" />
    <link rel="stylesheet" href="/static/css/style.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Username text styling */
        .username-text {
            font-size: 0.95rem !important;
            font-weight: 400;
        }

        /* Notification styles */
        .notification-badge {
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 0px 2px;
            font-size: 0.4rem;
            position: relative;
            top: -2px;
            margin-left: 5px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 12px;
            height: 12px;
        }

        .notification-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .notification-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80%;
            overflow-y: auto;
        }

        .notification-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
        }

        .notification-item.unread {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }

        .notification-item.order_cancelled {
            border-left: 4px solid #dc3545;
        }

        .notification-item.recent {
            border: 1px solid #28a745;
            background-color: #f8fff9;
        }

        .notification-date {
            color: #666;
            font-size: 12px;
        }

        .close-modal {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-modal:hover,
        .close-modal:focus {
            color: black;
            text-decoration: none;
        }
    </style>
</head>
<body>
 <header>
  <div class="top-bar">
    <div class="top-header">
      <div class="logo-left">
        <img src="/static/icons/logo.jpg" alt="Company Logo" class="logo" />
      </div>
      <div class="site-title">Russeykeo Computer</div>
      <!-- Hamburger button visible only on mobile -->
      <button
        class="nav-toggle"
        aria-label="Toggle navigation menu"
        aria-expanded="false"
      >
        &#9776;
      </button>
    </div>
    <nav>
      <ul>
        <li><a href="{{ url_for('show_dashboard') }}">Home</a></li>

        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">Categories</a>
          <ul class="dropdown">
            <li><a href="/products/category/multi/1,5">Laptops</a></li>
            <li><a href="/products/category/2">Desktops</a></li>
            <li><a href="/products/category/3">Accessories</a></li>
          </ul>
        </li>
        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">Products</a>
          <ul class="dropdown">
            <li><a href="/products/brand/dell">Dell</a></li>
            <li><a href="/products/brand/hp">HP</a></li>
            <li><a href="/products/brand/lenovo">Lenovo</a></li>
            <li><a href="/products/brand/asus">Asus</a></li>
            <li><a href="/products/brand/acer">Acer</a></li>
            <li><a href="/products/brand/razer">Razer</a></li>
          </ul>
        </li>
        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">About Company</a>
          <ul class="dropdown">
            <li><a href="{{ url_for('about') }}">About Us</a></li>
            <!-- <li><a href="{{ url_for('services') }}">Our Services</a></li>
            <li><a href="{{ url_for('privacy') }}">Privacy Policy</a></li> -->
          </ul>
        </li>
        <li><a href="{{ url_for('cart') }}">My Cart</a></li>
        {% if not session.username %}
        <li><a href="{{ url_for('auth.register') }}">Create Account</a></li>
        {% endif %}
        {% if session.username %}
          {% if session.role == 'customer' %}
          <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
          <li>
            <a href="#" onclick="showNotifications()" id="notifications-link">
              Notifications
              <span
                id="notification-badge"
                class="notification-badge"
                style="display: none;"
                >0</span
              >
            </a>
          </li>
          {% elif session.role in ['staff', 'admin', 'super_admin'] %}
          <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
          <li>
            <a href="#" onclick="showNotifications()" id="notifications-link">
              Notifications
              <span
                id="notification-badge"
                class="notification-badge"
                style="display: none;"
                >0</span
              >
            </a>
          </li>
          {% endif %}
        {% endif %}
        {% if session.username %}
        <li class="nav-item dropdown">
          <a
            class="nav-link dropdown-toggle d-flex align-items-center text-white"
            href="#"
            id="userDropdown"
            role="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <i class="bi bi-person-circle me-2"></i>
            <span class="username-text">{{ session.username }}</span>
          </a>
          <ul
            class="dropdown-menu dropdown-menu-end"
            aria-labelledby="userDropdown"
          >
            <li><hr class="dropdown-divider" /></li>
            <li>
              <a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}"
                ><i class="bi bi-box-arrow-right me-2"></i>Logout</a
              >
            </li>
          </ul>
        </li>
        {% else %}
        <li class="nav-item">
          <a
            href="/auth/login"
            class="btn btn-outline-light btn-sm d-flex align-items-center"
          >
            <i class="bi bi-person-plus me-2"></i> Login Account
          </a>
        </li>
        {% endif %}
        <li>
          <form
            class="search-container"
            action="/search"
            method="GET"
            onsubmit="return validateSearch()"
          >
            <button
              type="button"
              class="search-icon"
              tabindex="0"
              aria-label="Search products"
              style="background:none; border:none; padding:0; cursor:pointer;"
            >
              <img
                src="/static/icons/search.png"
                alt="Search Icon"
                style="height: 20px"
              />
            </button>
            <input
              type="text"
              name="q"
              class="search-input"
              placeholder="Search..."
              aria-label="Search products"
              required
            />
            <div class="search-suggestions"></div>
          </form>
        </li>
      </ul>
    </nav>
  </div>
</header>


    <div class="content"> 
        <div class="hero-container">
            <div class="hero-section active">
                <img src="/static/icons/product/macbook.png" alt="MacBook Pro with Retina display" class="hero-image">
                <div class="hero-details">
                    <h1>MacBook Pro</h1>
                    <p class="subheading">with Retina display</p>
                    <h2>$1999</h2>
                </div>
            </div>
            <div class="hero-section">
                <img src="/static/icons/product/asus zenbook.png" alt="Asus Zenbook with OLED display" class="hero-image">
                <div class="hero-details">
                    <h1>Asus Zenbook</h1>
                    <p class="subheading">with OLED display</p>
                    <h2>$2200</h2>
                </div>
            </div>
            <div class="hero-section">
                <img src="/static/icons/product/razer blackwidow.png" alt="Razer Blackwidow Chroma Red Switch Keyboard" class="hero-image">
                <div class="hero-details">
                    <h1>Razer Blackwidow Chroma</h1>
                    <p class="subheading">Red Switch Keyboard</p>
                    <h2>$199</h2>
                </div>
            </div>
            <div class="hero-section">
                <img src="/static/icons/product/asus rog swift.png" alt="Asus Rog Swift Nvidia G-Sync Monitor" class="hero-image">
                <div class="hero-details">
                    <h1>Asus Rog Swift</h1>
                    <p class="subheading">Nvidia G-Sync 21:9 aspect ratio Computer Monitor</p>
                    <h2>$399</h2>
                </div>
            </div>
        </div>
        <div class="navigation-dots">
            <span class="dot active" data-index="0"></span>
            <span class="dot" data-index="1"></span>
            <span class="dot" data-index="2"></span>
            <span class="dot" data-index="3"></span>
        </div>
        
        <div class="categories" >
             <div class="categories">
                <div class="category">
                    <img src="/static/icons/msi.png" alt="Accessories Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/ASUS.png" alt="Laptops Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/Lanovo.png" alt="Desktops Icon" class="category-icon">
                </div>
                
                 <div class="category">
                    <img src="/static/icons/Acer.png" alt="PC Components Icon" class="category-icon">
                </div>
                 <div class="category">
                    <img src="/static/icons/Dell.png" alt="PC Components Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/Razer.png" alt="PC Components Icon" class="category-icon">
                </div>
                <div class="category">
                    <img src="/static/icons/HP.png" alt="PC Components Icon" class="category-icon">
                </div>
                
            </div>
        </div>

        
  <div class="custom-container" style="margin: 50px;">
    <div class="row g-4">
        <!-- Categories Sidebar -->
        <div class="col-lg-2 col-md-3 col-12" style="margin: 120px 0px 0px 0px;">
            
            <a href="/products/category/multi/1,5" class="category-link" style="text-decoration: none; color: inherit;">
                <div class="category" style="margin: 10px;">
                    <img src="/static/icons/laptop.png" alt="Laptops Icon" class="category-icon" style="width: 40px; height: 40px;">
                    <h4>Laptops</h4>
                </div>
            </a>
            <a href="/products/category/2" class="category-link" style="text-decoration: none; color: inherit;">
                <div class="category" style="margin: 10px;">
                    <img src="/static/icons/desktop.png" alt="Desktops Icon" class="category-icon" style="width: 40px; height: 40px;">
                    <h4>Desktops</h4>
                </div>
            </a>
            <a href="/products/category/3" class="category-link" style="text-decoration: none; color: inherit;">
                <div class="category" style="margin: 10px;">
                    <img src="/static/icons/keyboard.png" alt="Accessories Icon" class="category-icon" style="width: 40px; height: 40px;">
                    <h4>Accessories</h4>
                </div>
            </a>
            
        </div>
        <!-- Laptops Products -->
        <div class="col-lg-10 col-md-9 col-12">
            <h1>{% if brand %}{{ brand.upper() }}{% elif category %}{% if category.name.lower() == 'laptops' or category.name.lower() == 'laptop' or category.name.lower() == 'laptop_gaming' %}Laptops{% elif category.name.lower() == 'desktops' or category.name.lower() == 'desktop' %}Desktops{% elif category.name.lower() == 'accessories' %}Accessories{% else %}{{ category.name.title() }}{% endif %}{% else %}Products in Category{% endif %}</h1>
        {% if products %}
        <div class="row g-4">
            {% for product in products %}
            <div class="col-lg-3 col-md-12">
                <div class="product-card card h-100">
                    <a href="{{ url_for('view_product_by_slug', product_slug=product.name|slugify) }}">
                        <img src="{% if product.photo %}/static/uploads/products/{{ product.photo }}{% else %}/static/images/placeholder-product.jpg{% endif %}" 
                             class="card-img-top p-3" 
                             alt="{{ product.name }}">
                    </a>
                    <div class="card-body">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="text-primary h5">${{ "%.2f"|format(product.price) }}</p>
                        <p class="card-text text-muted">{{ product.description|truncate(100) }}</p>
                        <div class="d-flex gap-2 mt-3">
                            <a href="{{ url_for('view_product_by_slug', product_slug=product.name|slugify) }}" class="btn btn-primary view-product-btn flex-fill">View Product</a>
                            {% if product.stock_quantity <= 0 %}
                                {% if product.allow_preorder %}
                                    <!-- Pre-Order Button -->
                                    <button class="btn cart-btn preorder-btn flex-fill"
                                            data-product-id="{{ product.id }}"
                                            data-product-name="{{ product.name }}"
                                            data-product-price="{{ product.price }}"
                                            data-expected-restock="{{ product.expected_restock_date or '' }}"
                                            data-product-image="{% if product.photo %}/static/uploads/products/{{ product.photo }}{% else %}/static/images/placeholder-product.jpg{% endif %}"
                                            title="Pre-Order this product"
                                            style="background-color: #ffc107; color: #000; border: none; display: inline-flex; align-items: center; justify-content: center;">
                                        <i class="bi bi-clock"></i>
                                    </button>
                                {% else %}
                                    <!-- Unavailable Button -->
                                    <button class="btn cart-btn flex-fill"
                                            disabled
                                            style="background-color: #6c757d; color: #fff; border: none; display: inline-flex; align-items: center; justify-content: center; cursor: not-allowed;">
                                        <i class="bi bi-x-circle"></i> Unavailable
                                    </button>
                                {% endif %}
                            {% else %}
                                <!-- Add to Cart Button -->
                                <button class="btn cart-btn add-to-cart-btn flex-fill"
                                        data-product-id="{{ product.id }}"
                                        title="Add to Cart"
                                        style="background-color: #28a745; color: #fff; border: none; display: inline-flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-cart-plus"></i>
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <p>No products found in this category.</p>
        {% endif %}
        </div>
    </div>
</div>

    <footer class="footer1">
        <div class="container1">
            <div class="row1">
                <div class="footer1-col">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="{{ url_for('about') }}">About Us</a></li>
                        <!-- <li><a href="{{ url_for('services') }}">Our Services</a></li>
                        <li><a href="{{ url_for('privacy') }}">Privacy Policy</a></li> -->
                       
                    </ul>
                </div>
                <div class="footer1-col">
                    <h4>Online Shop</h4>
                    <ul>
                        <li><a href="/products/category/multi/1,5">Laptops</a></li>
                        <li><a href="/products/category/2">Desktops</a></li>
                        <li><a href="/products/category/3">Accessories</a></li>
                    </ul>
                </div>
               <div class="footer1-col">
                 <div class="footer1-col">
                    <h4>Shop Address</h4>
                    <div class="qr-container">
                            <img src="/static/icons/QR1.png" alt="Shop QR Code">
                    </div>
                        <div class="address-text">
                    <a href="https://maps.app.goo.gl/asmvfLTkajoffidN6?g_st=com.google.maps.preview.copy" 
                    target="_blank" 
                    style="text-decoration: none; color: #007BFF;" 
                    onmouseover="this.style.color='#0056b3'" 
                    onmouseout="this.style.color='#007BFF'">
                        No. 829B, entrance to Russey Keo High School, <br>
                        Sangkat Russey Keo, Khan Russey Keo, <br>
                        Phnom Penh.
                    </a>
                </div>
                </div>
                </div>
            </div>
        </div>
    </footer>
    <script src="/static/js/unified-notifications.js"></script>
    <script src="/static/js/view_product_button.js"></script>
    <script src="/static/js/homepage.js"></script>
    <script src="/static/js/homepage_products_v2.js"></script>
    <script src="/static/js/brand_navigation.js"></script>

    <!-- Bootstrap JavaScript for dropdown functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Initialize cart state when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load cart state from localStorage
            const savedCartIds = localStorage.getItem('cartProductIds');
            if (savedCartIds) {
                try {
                    const cartIds = JSON.parse(savedCartIds);
                    cartProductIds = new Set(cartIds);

                    // Update button states for products already in cart (only for in-stock items)
                    cartIds.forEach(productId => {
                        const button = document.querySelector(`button[data-product-id="${productId}"].add-to-cart-btn`);
                        if (button) {
                            showAddedState(button);
                        }
                    });
                } catch (e) {
                    console.error('Error loading cart state:', e);
                    cartProductIds = new Set();
                }
            } else {
                cartProductIds = new Set();
            }

            // Function to show "Already in Cart" state
            function showAddedState(buttonElement) {
                // Show "Already in Cart" state - disabled and visually distinct
                buttonElement.style.backgroundColor = '#6c757d'; // Gray background
                buttonElement.style.color = '#fff'; // White text
                buttonElement.style.border = 'none';
                buttonElement.innerHTML = `
                    <i class="bi bi-check-circle"></i>
                `;
                buttonElement.disabled = true; // Disable button to prevent clicks
                buttonElement.title = 'This item is already in your cart';
            }

            // Add event listeners for add-to-cart and pre-order buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.add-to-cart-btn')) {
                    const button = e.target.closest('.add-to-cart-btn');
                    const productId = parseInt(button.getAttribute('data-product-id'));

                    // Add immediate visual feedback
                    addClickFeedback(button);

                    // Check if button is disabled (already in cart)
                    if (button.disabled) {
                        // Button is disabled, do nothing (item already in cart)
                        return;
                    } else {
                        // Button is enabled, so add to cart
                        addToCart(productId, button);
                    }
                } else if (e.target.closest('.preorder-btn')) {
                    const button = e.target.closest('.preorder-btn');
                    addClickFeedback(button);

                    // Use state manager for stateful pre-order handling
                    if (window.preorderStateManager) {
                        const productId = button.getAttribute('data-product-id');
                        const productData = {
                            id: parseInt(productId),
                            name: button.getAttribute('data-product-name'),
                            price: parseFloat(button.getAttribute('data-product-price')),
                            expected_restock_date: button.getAttribute('data-expected-restock')
                        };
                        window.preorderStateManager.handlePreorderButtonClick(button, productId, productData);
                    } else {
                        // Fallback to original modal
                        openPreOrderModal(button);
                    }
                }
            });
        });

        // Global variable to store current product data for pre-order
        let currentProductData = null;

        // Function to open pre-order modal
        function openPreOrderModal(buttonElement) {
            const productId = buttonElement.getAttribute('data-product-id');
            const productName = buttonElement.getAttribute('data-product-name');
            const productPrice = parseFloat(buttonElement.getAttribute('data-product-price'));
            const expectedRestock = buttonElement.getAttribute('data-expected-restock');
            const productImage = buttonElement.getAttribute('data-product-image');

            // Store current product data
            currentProductData = {
                id: parseInt(productId),
                name: productName,
                price: productPrice,
                expected_restock_date: expectedRestock
            };

            // Update modal content
            document.getElementById('preorder-product-name').textContent = productName;
            document.getElementById('preorder-product-price').textContent = `$${productPrice.toFixed(2)}`;

            // Set product image
            const productImageElement = document.getElementById('preorder-product-image');
            if (productImage && productImage !== 'None' && productImage !== '') {
                productImageElement.src = productImage;
                productImageElement.alt = productName;
            } else {
                productImageElement.src = '/static/images/placeholder-product.jpg';
                productImageElement.alt = 'Product Image';
            }

            const restockText = expectedRestock && expectedRestock !== 'None' ?
                expectedRestock :
                'To be announced';
            document.getElementById('preorder-expected-restock').textContent = restockText;

            // Update deposit amounts
            updateDepositAmounts();

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('preorderModal'));
            modal.show();
        }

        // Function to update deposit amounts based on product price
        function updateDepositAmounts() {
            if (!currentProductData) return;

            const quantity = 1; // Fixed quantity of 1 for pre-orders
            const totalPrice = currentProductData.price * quantity;

            document.getElementById('deposit25-amount').textContent = (totalPrice * 0.25).toFixed(2);
            document.getElementById('deposit50-amount').textContent = (totalPrice * 0.50).toFixed(2);
            document.getElementById('deposit100-amount').textContent = totalPrice.toFixed(2);
        }

        // Function to add click feedback (visual feedback when button is clicked)
        function addClickFeedback(buttonElement) {
            const originalTransform = buttonElement.style.transform;
            buttonElement.style.transform = 'scale(0.95)';
            setTimeout(() => {
                buttonElement.style.transform = originalTransform;
            }, 150);
        }

        // Handle pre-order confirmation
        document.addEventListener('DOMContentLoaded', function() {
            const confirmButton = document.getElementById('confirm-preorder');
            if (confirmButton) {
                confirmButton.addEventListener('click', submitPreOrder);
            }
        });

        // Function to submit pre-order
        async function submitPreOrder() {
            try {
                const quantity = 1; // Fixed quantity of 1 for pre-orders
                const depositPercentage = parseInt(document.querySelector('input[name="depositOption"]:checked').value);
                const notes = document.getElementById('preorder-notes').value;

                // Calculate the actual deposit amount to be paid
                const totalPrice = currentProductData.price * quantity;
                const depositAmount = (totalPrice * depositPercentage) / 100;

                const response = await fetch('/api/preorders/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_id: currentProductData.id,
                        quantity: quantity,
                        deposit_percentage: depositPercentage,
                        payment_method: null, // Payment will be handled in cart
                        notes: notes
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('preorderModal'));
                    modal.hide();

                    // Update pre-order state
                    if (window.preorderStateManager) {
                        window.preorderStateManager.setPreorderState(currentProductData.id, {
                            has_preorder: true,
                            preorder_id: data.pre_order_id,
                            status: 'pending'
                        });

                        // Update button state immediately
                        const preorderBtn = document.querySelector(`[data-product-id="${currentProductData.id}"].preorder-btn`);
                        if (preorderBtn) {
                            window.preorderStateManager.updateButtonState(preorderBtn, currentProductData.id);
                        }
                    }

                    // Dispatch event for other components
                    document.dispatchEvent(new CustomEvent('preorderCreated', {
                        detail: {
                            productId: currentProductData.id,
                            preorderId: data.pre_order_id,
                            status: 'pending'
                        }
                    }));

                    // Pass the deposit amount (not full price) to cart for payment
                    const priceForCart = depositAmount / quantity; // Price per item for cart display
                    addPreOrderToCartAndRedirect(data.pre_order_id, currentProductData.id, currentProductData.name, quantity, priceForCart);
                } else {
                    showNotification('Error placing pre-order: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('An error occurred while placing the pre-order.', 'error');
            }
        }

        // Function to add pre-order to cart and show success
        async function addPreOrderToCartAndRedirect(preOrderId, productId, productName, quantity, price) {
            console.log(`🛒 Adding pre-order #${preOrderId} to cart...`);

            try {
                const requestData = {
                    preorder_id: preOrderId,
                    product_id: productId,
                    quantity: quantity,
                    price: price
                };

                console.log('🛒 Sending request to add preorder to cart:', requestData);

                const response = await fetch('/api/cart/add-preorder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('📡 Response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('📦 Cart addition result:', result);

                    if (result.success) {
                        // Success - show success message with cart options
                        showNotification('Pre-order added to cart successfully!', 'success');
                        return;
                    }
                }

                // If we get here, something went wrong - still show success for pre-order creation
                console.warn('Cart addition failed, but pre-order was created');
                showNotification('Pre-order created successfully!', 'success');

            } catch (error) {
                console.error('❌ Error adding pre-order to cart:', error);
                showNotification('Pre-order created successfully!', 'success');
            }
        }
    </script>

    <!-- Include Pre-Order State Manager -->
    <script src="/static/js/preorder_state_manager.js"></script>

    <script>
        // Fix for My Pre-Orders navigation
        document.addEventListener('DOMContentLoaded', function() {
            const preOrdersLinks = document.querySelectorAll('a[href*="customer_preorders"]');
            preOrdersLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('Pre-orders link clicked from category page');
                    // Ensure navigation works
                    if (!e.defaultPrevented) {
                        window.location.href = this.href;
                    }
                });
            });

            // Smart pre-order state loading (efficient, cached)
            function loadCategoryPreorderStates() {
                if (typeof smartLoadPreorderStates === 'function') {
                    console.log('🔄 Category: Loading pre-order states (smart mode)');
                    smartLoadPreorderStates();
                } else if (window.preorderStateManager && window.preorderStateManager.isLoggedIn) {
                    console.log('🔄 Category: Using fallback state loading method');
                    // Fallback method - load states directly
                    const buttons = document.querySelectorAll('.preorder-btn[data-product-id]');
                    if (buttons.length > 0) {
                        const productIds = Array.from(buttons).map(btn => btn.getAttribute('data-product-id'));
                        console.log('🔄 Category: Loading states for products:', productIds);

                        window.preorderStateManager.loadStatesFromServer(productIds).then(() => {
                            buttons.forEach(button => {
                                const productId = button.getAttribute('data-product-id');
                                const state = window.preorderStateManager.getPreorderState(productId);
                                if (state.has_preorder) {
                                    console.log(`✅ Category: Product ${productId} has pre-order, showing green`);
                                    window.preorderStateManager.updateButtonState(button, productId);
                                }
                            });
                        });
                    }
                } else {
                    console.log('⚠️ Category: State manager not ready, retrying...');
                    setTimeout(loadCategoryPreorderStates, 500);
                }
            }

            // Start loading with a small delay to ensure everything is ready
            setTimeout(loadCategoryPreorderStates, 300);
        });
    </script>

    <!-- Notification Modal -->
    <div id="notificationModal" class="notification-modal">
        <div class="notification-content">
            <span class="close-modal" onclick="closeNotifications()">&times;</span>
            <h2>Notifications</h2>
            <div id="notificationsList">
                <p>Loading notifications...</p>
            </div>
            <div style="margin-top: 20px;">
                <button onclick="markAllAsRead()" class="btn btn-primary">Mark All as Read</button>
                <button onclick="clearAllNotifications()" class="btn btn-warning">Clear All</button>
                <button onclick="closeNotifications()" class="btn btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <!-- Pre-Order Modal -->
    <div class="modal fade" id="preorderModal" tabindex="-1" aria-labelledby="preorderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="preorderModalLabel">
                        <i class="bi bi-clock"></i> Place Pre-Order - Initial Deposit
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <img id="preorder-product-image" src="" alt="Product Image" class="img-fluid rounded" style="max-height: 200px; object-fit: contain;">
                        </div>
                        <div class="col-md-8">
                            <h4 id="preorder-product-name"></h4>
                            <p><strong>Price:</strong> <span class="text-primary h5" id="preorder-product-price"></span></p>
                            <p><strong>Expected Availability:</strong> <span class="text-muted" id="preorder-expected-restock"></span></p>
                        </div>
                    </div>

                    <hr>

                    <!-- Initial Deposit Options -->
                    <div class="mb-4">
                        <h6><i class="bi bi-credit-card"></i> Initial Deposit Options</h6>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Choose your initial deposit amount. Payment will be processed through the cart.
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="depositOption" id="deposit25" value="25" checked>
                            <label class="form-check-label" for="deposit25">
                                <strong>25% Initial Deposit</strong> - $<span id="deposit25-amount">0.00</span><br>
                                <small class="text-muted">Pay 25% now, remaining 75% when available</small>
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="depositOption" id="deposit50" value="50">
                            <label class="form-check-label" for="deposit50">
                                <strong>50% Initial Deposit</strong> - $<span id="deposit50-amount">0.00</span><br>
                                <small class="text-muted">Pay 50% now, remaining 50% when available</small>
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="depositOption" id="deposit100" value="100">
                            <label class="form-check-label" for="deposit100">
                                <strong>Full Payment</strong> - $<span id="deposit100-amount">0.00</span><br>
                                <small class="text-muted">Pay full amount now, no additional payment needed</small>
                            </label>
                        </div>
                    </div>

                    <!-- Special Requests -->
                    <div class="mb-4">
                        <label for="preorder-notes" class="form-label"><strong>Special Requests (Optional)</strong></label>
                        <textarea class="form-control" id="preorder-notes" rows="3" placeholder="Any special requests or notes..."></textarea>
                    </div>

                    <!-- Pre-order Terms -->
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Pre-order Terms:</strong>
                        <ul class="mb-0 mt-2">
                            <li>You will be notified when the product becomes available</li>
                            <li>Deposits are refundable if you cancel before the product arrives</li>
                            <li>You have 7 days to complete your purchase once notified</li>
                            <li>Prices may be subject to change at time of availability</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" id="confirm-preorder">
                        <i class="bi bi-clock"></i> Confirm Pre-Order
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Notification system
        let notifications = [];

        // Load notifications when page loads (for logged-in customers)
    </script>

    {% if session.username and session.role == 'customer' %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            // Check for new notifications every 30 seconds
            setInterval(loadNotifications, 30000);
        });
    </script>
    {% endif %}

    <script>
        function loadNotifications() {
            return fetch('/api/customer/notifications')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        notifications = data.notifications;
                        updateNotificationBadge();

                        // Show custom notification for new unread notifications (only once per session)
                        const unreadNotifications = notifications.filter(n => !n.is_read);
                        if (unreadNotifications.length > 0) {
                            const newNotifications = unreadNotifications.filter(n => {
                                return !sessionStorage.getItem(`notification_shown_${n.id}`);
                            });

                            if (newNotifications.length > 0) {
                                // Mark as shown in session storage
                                newNotifications.forEach(n => {
                                    sessionStorage.setItem(`notification_shown_${n.id}`, 'true');
                                });

                                // Show custom notification popup (no browser permission needed)
                                const latestNotification = newNotifications[0];
                                setTimeout(() => {
                                    showCustomNotificationPopup(latestNotification.message);
                                }, 1000);
                            }
                        }
                    }
                    return data; // Return the data for chaining
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    throw error; // Re-throw for proper error handling
                });
        }

        function updateNotificationBadge() {
            const badge = document.getElementById('notification-badge');
            const unreadCount = notifications.filter(n => !n.is_read).length;

            // Show badge only for unread notifications
            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.style.display = 'inline-flex';
                badge.style.backgroundColor = '#dc3545'; // Red for unread
                // Set balanced size styles
                badge.style.fontSize = '0.75rem';
                badge.style.minWidth = '20px';
                badge.style.height = '20px';
                badge.style.padding = '2px 6px';
                badge.style.marginLeft = '5px';
            } else {
                // Hide badge completely when all notifications are read
                badge.style.display = 'none';
            }
        }

        function showNotifications() {
            const modal = document.getElementById('notificationModal');
            const list = document.getElementById('notificationsList');

            if (notifications.length === 0) {
                list.innerHTML = '<p>No notifications</p>';
            } else {
                let html = '';
                const now = new Date();
                const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

                notifications.forEach(notification => {
                    const readClass = notification.is_read ? '' : 'unread';
                    const typeClass = notification.type || '';
                    const notificationDate = new Date(notification.created_date);
                    const isRecent = notificationDate > oneDayAgo;
                    const recentClass = isRecent ? 'recent' : '';

                    html += `
                        <div class="notification-item ${readClass} ${typeClass} ${recentClass}" data-id="${notification.id}">
                            <div>${notification.message} ${isRecent && notification.is_read ? '<span style="color: #28a745; font-size: 12px;">(Recent)</span>' : ''}</div>
                            <div class="notification-date">${notificationDate.toLocaleString()}</div>
                            ${!notification.is_read ? '<button onclick="markAsRead(' + notification.id + ')" class="btn btn-sm btn-outline-primary" style="margin-top: 5px;">Mark as Read</button>' : ''}
                        </div>
                    `;
                });
                list.innerHTML = html;
            }

            modal.style.display = 'block';
        }

        function closeNotifications() {
            document.getElementById('notificationModal').style.display = 'none';
        }

        function markAsRead(notificationId) {
            // Disable the button immediately to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Marking...';

            fetch(`/api/customer/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Only refresh the notifications data and modal once
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
                // Re-enable button on error
                button.disabled = false;
                button.textContent = 'Mark as Read';
            });
        }

        function markAllAsRead() {
            // Disable the button to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Marking All...';

            fetch('/api/customer/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Only refresh once with proper chaining
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
                // Re-enable button on error
                button.disabled = false;
                button.textContent = 'Mark All as Read';
            });
        }

        async function clearAllNotifications() {
            // Show custom confirmation modal
            const confirmed = await showClearNotificationsConfirmation();
            if (!confirmed) {
                return;
            }

            // Disable the button to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Clearing All...';

            fetch('/api/customer/notifications/clear-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh notifications and update badge
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                } else {
                    window.unifiedNotifications.error('Error clearing notifications: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error clearing all notifications:', error);
                window.unifiedNotifications.error('An error occurred while clearing notifications.');
            })
            .finally(() => {
                // Re-enable button
                button.disabled = false;
                button.textContent = 'Clear All';
            });
        }

        function showClearNotificationsConfirmation() {
            return new Promise((resolve) => {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.className = 'clear-notifications-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    backdrop-filter: blur(2px);
                `;

                // Create modal
                const modal = document.createElement('div');
                modal.style.cssText = `
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                    max-width: 400px;
                    width: 90%;
                    transform: scale(0.9);
                    transition: transform 0.2s ease-out;
                `;

                modal.innerHTML = `
                    <div style="padding: 24px 24px 16px 24px; text-align: center;">
                        <div style="color: #f59e0b; font-size: 48px; margin-bottom: 16px;">🗑️</div>
                        <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600; color: #111827;">Clear All Notifications</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px;">Are you sure you want to clear all notifications? This action cannot be undone.</p>
                    </div>
                    <div style="padding: 16px 24px 24px 24px; display: flex; gap: 12px; justify-content: center;">
                        <button class="cancel-btn" style="
                            background: #f3f4f6;
                            color: #374151;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Cancel</button>
                        <button class="confirm-btn" style="
                            background: #f59e0b;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Clear All</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                // Animate in
                requestAnimationFrame(() => {
                    modal.style.transform = 'scale(1)';
                });

                // Add hover effects
                const cancelBtn = modal.querySelector('.cancel-btn');
                const confirmBtn = modal.querySelector('.confirm-btn');

                cancelBtn.addEventListener('mouseenter', () => {
                    cancelBtn.style.backgroundColor = '#e5e7eb';
                });
                cancelBtn.addEventListener('mouseleave', () => {
                    cancelBtn.style.backgroundColor = '#f3f4f6';
                });

                confirmBtn.addEventListener('mouseenter', () => {
                    confirmBtn.style.backgroundColor = '#d97706';
                });
                confirmBtn.addEventListener('mouseleave', () => {
                    confirmBtn.style.backgroundColor = '#f59e0b';
                });

                // Handle button clicks
                const cleanup = () => {
                    modal.style.transform = 'scale(0.9)';
                    overlay.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 200);
                };

                cancelBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(false);
                });

                confirmBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(true);
                });

                // Close on overlay click
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        cleanup();
                        resolve(false);
                    }
                });

                // Close on escape key
                const handleEscape = (e) => {
                    if (e.key === 'Escape') {
                        cleanup();
                        resolve(false);
                        document.removeEventListener('keydown', handleEscape);
                    }
                };
                document.addEventListener('keydown', handleEscape);
            });
        }

        function showCustomNotificationPopup(message) {
            // Create custom notification popup (no browser permission needed)
            const popup = document.createElement('div');
            popup.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                max-width: 350px;
                animation: slideInRight 0.3s ease-out;
            `;

            popup.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div>
                        <div style="font-weight: bold; margin-bottom: 8px;">New Notification</div>
                        <div style="font-size: 14px; line-height: 1.4;">${message}</div>
                        <div style="margin-top: 12px;">
                            <button onclick="showNotifications(); this.parentElement.parentElement.parentElement.remove();" style="background: white; color: #007bff; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 8px;">View All</button>
                            <button onclick="this.parentElement.parentElement.parentElement.remove();" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">Dismiss</button>
                        </div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove();" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; margin-left: 10px;">&times;</button>
                </div>
            `;

            document.body.appendChild(popup);

            // Auto-remove after 8 seconds
            setTimeout(() => {
                if (popup.parentNode) {
                    popup.remove();
                }
            }, 8000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('notificationModal');
            if (event.target == modal) {
                closeNotifications();
            }
        }

        // Apply consistent card layout and button positioning (matching homepage)
        function applyCardLayoutFixes() {
            requestAnimationFrame(() => {
                const cards = document.querySelectorAll('.product-card.card');
                cards.forEach(card => {
                    card.style.minHeight = '400px';
                    const cardBody = card.querySelector('.card-body');
                    if (cardBody) {
                        cardBody.style.display = 'flex';
                        cardBody.style.flexDirection = 'column';
                        cardBody.style.height = '100%';
                    }
                    const buttonContainer = card.querySelector('.d-flex');
                    if (buttonContainer) {
                        buttonContainer.style.marginTop = 'auto';
                    }
                });

                setTimeout(() => {
                    // Force layout recalculation
                    document.body.offsetHeight;
                }, 50);
            });
        }

        // Apply layout fixes when page loads
        document.addEventListener('DOMContentLoaded', applyCardLayoutFixes);

        // Apply layout fixes after any dynamic content changes
        window.addEventListener('load', applyCardLayoutFixes);


    

document.addEventListener('DOMContentLoaded', () => {
  const navToggle = document.querySelector('.nav-toggle');
  const nav = document.querySelector('nav');
  const searchContainer = document.querySelector('.search-container');
  const searchIcon = document.querySelector('.search-icon');
  const searchInput = document.querySelector('.search-input');

  // Toggle nav menu on hamburger click
  navToggle.addEventListener('click', () => {
    const expanded = nav.classList.toggle('nav-open');
    navToggle.setAttribute('aria-expanded', expanded);
  });

  // Toggle search input on search icon click
  searchIcon.addEventListener('click', () => {
    searchContainer.classList.toggle('active');
    if (searchContainer.classList.contains('active')) {
      searchInput.focus();
    } else {
      searchInput.value = '';
    }
  });

  // Close nav or search if clicked outside
  document.addEventListener('click', (e) => {
    if (
      !nav.contains(e.target) &&
      !navToggle.contains(e.target)
    ) {
      nav.classList.remove('nav-open');
      navToggle.setAttribute('aria-expanded', false);
    }
    if (
      !searchContainer.contains(e.target)
    ) {
      searchContainer.classList.remove('active');
      searchInput.value = '';
    }
  });
});
    </script>
</body>
</html>
