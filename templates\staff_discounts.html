{% extends "base.html" %}

{% block title %}Discount Management - Staff Dashboard{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_discounts.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
{% endblock %}

{% block content %}
<div class="staff-container">
    <h1 style="display: flex; justify-content: center;"><i class="fas fa-percentage"></i> Discount Management</h1>

    <!-- Discount Management Button -->
    {% if session.get('role') != 'staff' %}
    <div class="discount-section">
        <h2><i class="fas fa-plus-circle"></i> Discount Management</h2>
        <p>Manage product discounts and promotional offers through our advanced discount manager.</p>
        <button class="btn btn-primary" onclick="openDiscountModal()">
            <i class="fas fa-percentage"></i> Open Discount Manager
        </button>
    </div>
    {% endif %}

    <!-- Current Discounts Section -->
    <div class="section-header">
        <h2><i class="fas fa-tags"></i> Current Discounts</h2>
        {% if session.get('role') != 'staff' %}
        <button id="remove-all-discounts-btn" class="btn btn-danger" onclick="clearAllDiscounts()" style="display: none;">
            <i class="fas fa-trash-alt"></i> Remove All Discounts
        </button>
        {% endif %}
    </div>

    <div id="discounts-container">
        <div id="current-discounts-table">
            <p>Loading current discounts...</p>
        </div>
        <div id="mobile-discounts-list" style="display: none;">
            <p>Loading current discounts...</p>
        </div>

        <!-- Pagination -->
        <nav aria-label="Discounts pagination" class="mt-3">
            <ul class="pagination justify-content-center" id="discounts-pagination"></ul>
        </nav>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="message-container"></div>

<!-- Confirmation Modal -->
<div id="confirmation-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <h3>Confirm Action</h3>
        <p id="confirmation-message"></p>
        <div class="modal-buttons">
            <button class="btn btn-secondary" onclick="closeConfirmationModal()">Cancel</button>
            <button class="btn btn-danger" id="confirm-action-btn">Confirm</button>
        </div>
    </div>
</div>

<!-- Discount Management Modal -->
<div id="discount-modal" class="discount-modal" style="display: none;">
    <div class="discount-modal-overlay" onclick="closeDiscountModal()"></div>
    <div class="discount-modal-content">
        <div class="discount-modal-header">
            <h2><i class="fas fa-percentage"></i> Discount Management</h2>
            <button class="discount-modal-close" onclick="closeDiscountModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="discount-modal-body">
            <!-- Discount Management Tabs -->
            <div class="discount-modal-tabs">
                <div class="discount-tab-buttons">
                    <button class="discount-tab-btn active" data-tab="single">Single Product</button>
                    <button class="discount-tab-btn" data-tab="category">Category Discount</button>
                    <button class="discount-tab-btn" data-tab="brand">Brand Discount</button>
                    <button class="discount-tab-btn" data-tab="volume">Volume Discounts</button>
                </div>

                <!-- Single Product Discount Tab -->
                <div class="discount-tab-content active" id="modal-single-tab">
                    <div class="discount-modal-form">
                        <h3><i class="fas fa-tag"></i> Apply Discount to Single Product</h3>
                        <form id="modal-single-discount-form">
                            <div class="discount-form-group">
                                <label for="modal-product-search">Select Product:</label>

                                <!-- Search Input -->
                                <div class="product-search-container">
                                    <input type="text" id="modal-product-search" placeholder="🔍 Search products... (name, model number, category)" autocomplete="off">
                                    <input type="hidden" id="modal-product-id" name="product_id" required>
                                </div>

                                <!-- Quick Filters Section -->
                                <div class="quick-filters-section">
                                    <div class="quick-filters-header">
                                        <h4>Quick Filters:</h4>
                                    </div>
                                    <div class="quick-filters-buttons">
                                        <button type="button" class="quick-filter-btn" id="recent-filter-btn" title="Products you've discounted recently">
                                            <i class="fas fa-history"></i> Your Recent Work <span class="filter-count" id="recent-count">(0)</span>
                                        </button>
                                        <button type="button" class="quick-filter-btn" id="discount-filter-btn" title="All products currently on discount store-wide">
                                            <i class="fas fa-tags"></i> All Store Discounts <span class="filter-count" id="discount-count">(0)</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Recently Used Products -->
                                <div class="recently-used-section" id="recently-used-section" style="display: none;">
                                    <div class="recently-used-header">
                                        <h4><i class="fas fa-history"></i> Your Recent Work:</h4>
                                        <button type="button" class="close-section-btn" id="close-recent-btn" title="Hide this section">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="recently-used-list" id="recently-used-list">
                                        <!-- Recently used products will be populated here -->
                                    </div>
                                </div>

                                <!-- Search Results -->
                                <div class="search-results" id="search-results" style="display: none;">
                                    <div class="search-results-header">
                                        <div class="results-info">
                                            <span class="results-count">Search Results: (showing 0 of 0)</span>
                                            <div class="bulk-selection-info" id="bulk-selection-info" style="display: none;">
                                                <span class="selected-count">Selected: <strong>0</strong> products</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="select-all-btn">Select All</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="clear-selection-btn">Clear All</button>
                                            </div>
                                        </div>
                                        <div class="results-per-page">
                                            <label for="results-per-page-select">Show:</label>
                                            <select id="results-per-page-select">
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                            </select>
                                            <span>per page</span>
                                        </div>
                                    </div>
                                    <div class="search-results-container">
                                        <div class="search-results-list" id="search-results-list">
                                            <!-- Results will be populated here -->
                                        </div>
                                        <!-- Sticky Pagination Footer -->
                                        <div class="search-results-footer sticky-pagination" id="search-results-footer" style="display: none;">
                                            <div class="pagination-wrapper">
                                                <nav aria-label="Search results pagination">
                                                    <ul class="pagination pagination-sm justify-content-center mb-0" id="pagination-container">
                                                        <!-- Pagination will be dynamically generated -->
                                                    </ul>
                                                </nav>
                                                <div class="pagination-info">
                                                    <span id="pagination-info-text">Showing 1-10 of 100 results</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bulk Operations Section -->
                                <div class="bulk-operations" id="bulk-operations" style="display: none;">
                                    <div class="bulk-operations-header">
                                        <h6><i class="fas fa-layer-group"></i> Bulk Discount Operations</h6>
                                        <span class="bulk-selected-count">0 products selected</span>
                                    </div>
                                    <div class="bulk-operations-content">
                                        <div class="bulk-selected-products" id="bulk-selected-products">
                                            <!-- Selected products will be listed here -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Selected Product Display -->
                                <div class="selected-product" id="selected-product" style="display: none;">
                                    <div class="selected-product-info">
                                        <span class="selected-product-name"></span>
                                        <span class="selected-product-price"></span>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="change-product">Change Product</button>
                                </div>
                            </div>


                            <div class="discount-form-group">
                                <label for="modal-single-discount">Discount Percentage:</label>
                                <div class="discount-input-group">
                                    <input type="number" id="modal-single-discount" name="discount_percentage"
                                           min="1" max="90" step="1" required>
                                    <span class="discount-input-suffix">%</span>
                                </div>
                            </div>
                            <div class="discount-form-group">
                                <button type="submit" class="btn btn-primary" id="apply-discount-btn">
                                    <i class="fas fa-percentage"></i> <span id="apply-discount-text">Apply Discount</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Category Discount Tab -->
                <div class="discount-tab-content" id="modal-category-tab">
                    <div class="discount-modal-form">
                        <h3><i class="fas fa-layer-group"></i> Apply Discount to Category</h3>
                        <form id="modal-category-discount-form">
                            <div class="discount-form-group">
                                <label for="modal-category-select">Select Category:</label>
                                <select id="modal-category-select" name="category_id" required>
                                    <option value="">Choose a category...</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="discount-form-group">
                                <label for="modal-category-discount">Discount Percentage:</label>
                                <div class="discount-input-group">
                                    <input type="number" id="modal-category-discount" name="discount_percentage"
                                           min="1" max="50" step="1" required>
                                    <span class="discount-input-suffix">%</span>
                                </div>
                                <small class="discount-form-note">Maximum 50% for category-wide discounts</small>
                            </div>
                            <div class="discount-form-group">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-layer-group"></i> Apply to All Products in Category
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Brand Discount Tab -->
                <div class="discount-tab-content" id="modal-brand-tab">
                    <div class="discount-modal-form">
                        <h3><i class="fas fa-copyright"></i> Apply Discount to Brand</h3>
                        <form id="modal-brand-discount-form">
                            <div class="discount-form-group">
                                <label for="modal-brand-select">Select Brand:</label>
                                <select id="modal-brand-select" name="brand_name" required>
                                    <option value="">Choose a brand...</option>
                                    {% for brand in brands %}
                                    <option value="{{ brand.brand }}">{{ brand.brand }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="discount-form-group">
                                <label for="modal-brand-discount">Discount Percentage:</label>
                                <div class="discount-input-group">
                                    <input type="number" id="modal-brand-discount" name="discount_percentage"
                                           min="1" max="40" step="1" required>
                                    <span class="discount-input-suffix">%</span>
                                </div>
                                <small class="discount-form-note">Maximum 40% for brand-wide discounts</small>
                            </div>
                            <div class="discount-form-group">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-copyright"></i> Apply to All Brand Products
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Volume Discount Tab -->
                <div class="discount-tab-content" id="modal-volume-tab">
                    <div class="discount-modal-form">
                        <h3><i class="fas fa-shopping-cart"></i> Volume Discount Management</h3>

                        <!-- Current Volume Discount Rules -->
                        <div class="volume-rules-section">
                            <div class="section-header">
                                <h4><i class="fas fa-layer-group"></i> Current Volume Discount Rules</h4>
                                <button type="button" class="btn btn-success btn-sm" onclick="openAddVolumeRuleModal()">
                                    <i class="fas fa-plus"></i> Add New Rule
                                </button>
                            </div>

                            <div id="volume-rules-container">
                                <p>Loading volume discount rules...</p>
                            </div>
                        </div>

                        <!-- Add/Edit Volume Rule Form -->
                        <div class="volume-rule-form" id="volume-rule-form" style="display: none;">
                            <h4 id="volume-form-title"><i class="fas fa-plus-circle"></i> Add Volume Discount Rule</h4>
                            <form id="volume-discount-form">
                                <input type="hidden" id="volume-rule-id" name="rule_id">

                                <div class="discount-form-group">
                                    <label for="volume-rule-name">Rule Name:</label>
                                    <input type="text" id="volume-rule-name" name="name"
                                           placeholder="e.g., Business Bulk Discount" required>
                                </div>

                                <div class="discount-form-group">
                                    <label for="volume-minimum-amount">Minimum Order Amount:</label>
                                    <div class="discount-input-group">
                                        <span class="discount-input-prefix">$</span>
                                        <input type="number" id="volume-minimum-amount" name="minimum_amount"
                                               min="1" step="0.01" placeholder="500.00" required>
                                    </div>
                                </div>

                                <div class="discount-form-group">
                                    <label for="volume-discount-percentage">Discount Percentage:</label>
                                    <div class="discount-input-group">
                                        <input type="number" id="volume-discount-percentage" name="discount_percentage"
                                               min="1" max="50" step="0.1" placeholder="10.0" required>
                                        <span class="discount-input-suffix">%</span>
                                    </div>
                                    <small class="discount-form-note">Maximum 50% for volume discounts</small>
                                </div>

                                <div class="discount-form-group">
                                    <label for="volume-max-discount">Maximum Discount Amount (Optional):</label>
                                    <div class="discount-input-group">
                                        <span class="discount-input-prefix">$</span>
                                        <input type="number" id="volume-max-discount" name="max_discount_amount"
                                               min="0" step="0.01" placeholder="100.00">
                                    </div>
                                    <small class="discount-form-note">Leave empty for no limit</small>
                                </div>

                                <div class="discount-form-group">
                                    <label for="volume-description">Description (Optional):</label>
                                    <textarea id="volume-description" name="description" rows="2"
                                              placeholder="Internal description for this rule"></textarea>
                                </div>

                                <div class="discount-form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> <span id="volume-submit-text">Save Rule</span>
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="cancelVolumeRuleForm()">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Pass user role to JavaScript
    window.userRole = '{{ session.get("role", "staff") }}';
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
<script src="{{ url_for('static', filename='js/item-counter.js') }}"></script>
<script src="{{ url_for('static', filename='js/staff_messages.js') }}"></script>
<script src="{{ url_for('static', filename='js/staff_discounts.js') }}"></script>
{% endblock %}
