#!/usr/bin/env python3
"""
Script to delete a pre-order and all related data from the database
"""

import mysql.connector
from config import Config

def delete_preorder(pre_order_id=57):
    try:
        # Connect to database
        conn = mysql.connector.connect(
            host=Config.MYSQL_HOST,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DB
        )
        
        cur = conn.cursor(dictionary=True)
        
        print(f"🗑️ Deleting Pre-Order #{pre_order_id}")
        print("=" * 50)
        
        # First, check if pre-order exists
        cur.execute("SELECT * FROM pre_orders WHERE id = %s", (pre_order_id,))
        preorder = cur.fetchone()
        
        if not preorder:
            print(f"❌ Pre-order #{pre_order_id} not found")
            return
            
        print(f"📦 Found pre-order: {preorder['id']}")
        print(f"👤 Customer ID: {preorder['customer_id']}")
        print(f"🛍️ Product ID: {preorder['product_id']}")
        print(f"📊 Status: {preorder['status']}")
        
        # Delete related payments first
        print("\n🔄 Deleting related payments...")
        cur.execute("SELECT COUNT(*) as count FROM pre_order_payments WHERE pre_order_id = %s", (pre_order_id,))
        payment_count = cur.fetchone()['count']
        
        if payment_count > 0:
            cur.execute("DELETE FROM pre_order_payments WHERE pre_order_id = %s", (pre_order_id,))
            print(f"✅ Deleted {payment_count} payment records")
        else:
            print("ℹ️ No payment records found")
        
        # Delete any linked orders
        print("\n🔄 Checking for linked orders...")
        cur.execute("SELECT COUNT(*) as count FROM orders WHERE pre_order_id = %s", (pre_order_id,))
        order_count = cur.fetchone()['count']
        
        if order_count > 0:
            # Get order IDs first
            cur.execute("SELECT id FROM orders WHERE pre_order_id = %s", (pre_order_id,))
            order_ids = [row['id'] for row in cur.fetchall()]
            
            # Delete order items first
            for order_id in order_ids:
                cur.execute("DELETE FROM order_items WHERE order_id = %s", (order_id,))
            
            # Delete orders
            cur.execute("DELETE FROM orders WHERE pre_order_id = %s", (pre_order_id,))
            print(f"✅ Deleted {order_count} linked orders and their items")
        else:
            print("ℹ️ No linked orders found")
        
        # Finally, delete the pre-order itself
        print("\n🔄 Deleting pre-order...")
        cur.execute("DELETE FROM pre_orders WHERE id = %s", (pre_order_id,))
        
        # Commit all changes
        conn.commit()
        
        print(f"✅ Pre-order #{pre_order_id} and all related data deleted successfully!")
        print("🎯 You can now create a fresh pre-order if needed.")
        
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Error: {e}")
        if conn:
            conn.rollback()
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()
        print("🔌 Database connection closed")

if __name__ == "__main__":
    # Confirm deletion
    response = input("⚠️ Are you sure you want to delete Pre-Order #53? (yes/no): ")
    if response.lower() in ['yes', 'y']:
        delete_preorder(57)
    else:
        print("❌ Deletion cancelled")
