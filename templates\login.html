<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Computer Shop</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body, html {
        height: 100%;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        overflow: hidden;
    }

    .container {
        display: flex;
        height: 100vh;
        position: relative;
    }

    .login-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .login-container {
        background: rgba(255, 255, 255, 0.95);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        padding: 3rem 2.5rem;
        border-radius: 24px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
        text-align: center;
        width: 100%;
        max-width: 420px;
        position: relative;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-radius: 24px;
        pointer-events: none;
    }

    .login-container h1 {
        color: #1a1a1a;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        letter-spacing: -0.02em;
    }

    .login-container .subtitle {
        color: #6b7280;
        font-size: 1rem;
        margin-bottom: 2rem;
        font-weight: 400;
    }

    .form-group {
        margin-bottom: 1.5rem;
        text-align: left;
        position: relative;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #374151;
        font-weight: 500;
        font-size: 0.875rem;
        letter-spacing: 0.025em;
    }

    .form-group input {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        background: #ffffff;
        font-size: 1rem;
        transition: all 0.2s ease;
        outline: none;
    }

    .form-group input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn {
        width: 100%;
        padding: 0.875rem 1.5rem;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .btn-secondary {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .btn-secondary:hover {
        background: #e5e7eb;
        transform: translateY(-1px);
    }

    .auth-links {
        text-align: center;
        margin-top: 1.5rem;
    }

    .auth-links p {
        color: #6b7280;
        font-size: 0.875rem;
        margin: 0;
    }

    .auth-links a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .auth-links a:hover {
        color: #764ba2;
        text-decoration: underline;
    }

    .flash-messages {
        margin-bottom: 1.5rem;
        padding: 1rem;
        border-radius: 12px;
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
        font-size: 0.875rem;
    }

    .flash-messages p {
        margin: 0;
    }

    /* Floating animation for background elements */
    .bg-decoration {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
    }

    .bg-decoration:nth-child(1) {
        width: 80px;
        height: 80px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }

    .bg-decoration:nth-child(2) {
        width: 120px;
        height: 120px;
        top: 70%;
        right: 10%;
        animation-delay: 2s;
    }

    .bg-decoration:nth-child(3) {
        width: 60px;
        height: 60px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    @media (max-width: 768px) {
        .login-container {
            margin: 1rem;
            padding: 2rem 1.5rem;
        }

        .login-container h1 {
            font-size: 1.75rem;
        }
    }
</style>
</head>
<body>
    <div class="container">
        <!-- Background decorations -->
        <div class="bg-decoration"></div>
        <div class="bg-decoration"></div>
        <div class="bg-decoration"></div>

        <div class="login-section">
            <div class="login-container">
                <h1>Welcome Back</h1>
                <p class="subtitle">Sign in to your Computer Shop account</p>

                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        <div class="flash-messages">
                            {% for message in messages %}
                                <p>{{ message }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                <form method="POST" action="{{ url_for('auth.login') }}">
                    <div class="form-group">
                        <label for="username">Email or Username</label>
                        <input type="text" id="username" name="username" required placeholder="Enter your email or username">
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required placeholder="Enter your password">
                    </div>
                    <button type="submit" class="btn btn-primary">Sign In</button>
                </form>

                <button type="button" class="btn btn-secondary" onclick="window.location.href='/'">
                    <i class="fas fa-arrow-left"></i> Back to Home
                </button>

                <div class="auth-links">
                    <p>Don't have an account? <a href="{{ url_for('register') }}">Create one here</a></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>