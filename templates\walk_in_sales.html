{% extends "base.html" %}

{% block title %}Walk-in Sales - POS System{% endblock %}

{% block content %}
<div class="walk-in-container">
    <div class="walk-in-header">
        <h1><i class="fas fa-store"></i> Walk-in Sales</h1>
        <div class="header-actions">
            <button id="clear-cart-btn" class="btn btn-warning">
                <i class="fas fa-trash"></i> Clear Cart
            </button>
            <button id="new-sale-btn" class="btn btn-success">
                <i class="fas fa-plus"></i> New Sale
            </button>
        </div>
    </div>

    <div class="walk-in-layout">
        <!-- Left Panel - Product Selection -->
        <div class="product-panel">
            <div class="search-section">
                <div class="search-bar">
                    <input type="text" id="product-search" placeholder="Search products by name or model number..." class="form-control">
                </div>
                <div class="quick-filters">
                    <button class="filter-btn active" data-category="all">All Products</button>
                    <button class="filter-btn discount-filter" data-category="discounted">💰 On Sale</button>
                    <button class="filter-btn" data-category="laptops">Laptops</button>
                    <button class="filter-btn" data-category="desktops">Desktops</button>
                    <button class="filter-btn" data-category="accessories">Accessories</button>
                </div>
            </div>

            <div class="products-grid" id="products-grid">
                <!-- Products will be loaded here dynamically -->
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> Loading products...
                </div>
            </div>

            <div class="pagination-container" id="pagination-container">
                <!-- Pagination will be added here -->
            </div>
        </div>

        <!-- Right Panel - Cart & Checkout -->
        <div class="cart-panel">
            <div class="cart-header">
                <h3><i class="fas fa-shopping-cart"></i> Current Sale</h3>
                <span class="cart-count" id="cart-count">0 items</span>
            </div>

            <div class="cart-items" id="cart-items">
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>No items in cart</p>
                    <small>Search and select products to add to cart</small>
                </div>
            </div>

            <div class="cart-summary" id="cart-summary" style="display: none;">
                <div class="summary-row">
                    <span>Subtotal:</span>
                    <span id="subtotal">$0.00</span>
                </div>
                <div class="summary-row">
                    <span>Tax (0%):</span>
                    <span id="tax">$0.00</span>
                </div>
                <div class="summary-row total">
                    <span>Total:</span>
                    <span id="total">$0.00</span>
                </div>
            </div>

            <!-- Customer Information Summary -->
            <div class="customer-section" id="customer-section" style="display: none;">
                <h4><i class="fas fa-user"></i> Customer Information</h4>
                <div class="customer-summary" id="customer-summary">
                    <div class="customer-info-display">
                        <span class="customer-name-display">Walk-in Customer</span>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="edit-customer-btn">
                            <i class="fas fa-edit"></i> Add/Edit Info
                        </button>
                    </div>
                    <div class="customer-details" id="customer-details" style="display: none;">
                        <!-- Customer details will be shown here -->
                    </div>
                </div>
            </div>

            <!-- Payment Section -->
            <div class="payment-section" id="payment-section" style="display: none;">
                <h4><i class="fas fa-credit-card"></i> Payment Method</h4>
                <div class="payment-methods">
                    <button class="payment-btn active" data-method="khqr">
                        <i class="fas fa-qrcode"></i>
                        <span>KHQR Payment</span>
                    </button>
                    <button class="payment-btn" data-method="cash">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Cash Payment</span>
                    </button>
                </div>

                <!-- KHQR Payment Details -->
                <div class="payment-details khqr-details active" id="khqr-details">
                    <div class="qr-container">
                        <div class="qr-placeholder">
                            <i class="fas fa-qrcode"></i>
                            <p>QR Code will be generated</p>
                        </div>
                    </div>
                    <p class="payment-instruction">Customer scans QR code to pay</p>
                </div>

                <!-- Cash Payment Details -->
                <div class="payment-details cash-details" id="cash-details">
                    <div class="cash-input">
                        <label for="cash-received">Cash Received:</label>
                        <input type="number" id="cash-received" placeholder="0.00" step="0.01" class="form-control">
                    </div>
                    <div class="change-display" id="change-display" style="display: none;">
                        <span>Change Due:</span>
                        <span class="change-amount" id="change-amount">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons" id="action-buttons" style="display: none;">
                <button id="process-payment-btn" class="btn btn-success btn-lg">
                    <i class="fas fa-check"></i> Process Payment
                </button>
                <button id="save-quote-btn" class="btn btn-secondary">
                    <i class="fas fa-save"></i> Save as Quote
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Modal -->
<div class="modal fade" id="invoice-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-receipt"></i> Invoice Generated
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close">
                    <span class="visually-hidden">Close</span>
                </button>
            </div>
            <div class="modal-body" id="invoice-content">
                <!-- Invoice content will be generated here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" id="print-invoice-btn" class="btn btn-primary">
                    <i class="fas fa-print"></i> Print Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Information Modal -->
<div class="modal fade" id="customer-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> Customer Information
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Optional:</strong> You can skip this step for anonymous walk-in customers, or add customer details for better tracking and future reference.
                </div>

                <form id="customer-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal-first-name" class="form-label">
                                    <i class="fas fa-user"></i> First Name
                                </label>
                                <input type="text" class="form-control" id="modal-first-name" placeholder="Enter first name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal-last-name" class="form-label">
                                    <i class="fas fa-user"></i> Last Name
                                </label>
                                <input type="text" class="form-control" id="modal-last-name" placeholder="Enter last name">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal-phone" class="form-label">
                                    <i class="fas fa-phone"></i> Phone Number
                                </label>
                                <input type="tel" class="form-control" id="modal-phone" placeholder="Enter phone number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal-email" class="form-label">
                                    <i class="fas fa-envelope"></i> Email Address
                                </label>
                                <input type="email" class="form-control" id="modal-email" placeholder="Enter email address">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="modal-address" class="form-label">
                            <i class="fas fa-map-marker-alt"></i> Address
                        </label>
                        <textarea class="form-control" id="modal-address" rows="3" placeholder="Enter full address (optional)"></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="save-customer">
                            <label class="form-check-label" for="save-customer">
                                <i class="fas fa-save"></i> Save customer information for future orders
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="skip-customer-btn">
                    <i class="fas fa-forward"></i> Skip (Anonymous Customer)
                </button>
                <button type="button" class="btn btn-primary" id="save-customer-btn">
                    <i class="fas fa-check"></i> Save & Continue
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="success-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> Sale Completed
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h4>Payment Successful!</h4>
                <p id="success-message">Sale has been processed successfully.</p>
                <div class="sale-summary" id="sale-summary">
                    <!-- Sale summary will be displayed here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="new-sale-modal-btn">
                    <i class="fas fa-plus"></i> New Sale
                </button>
                <button type="button" class="btn btn-primary" id="view-invoice-btn">
                    <i class="fas fa-receipt"></i> View Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container" class="notification-container"></div>

<link rel="stylesheet" href="{{ url_for('static', filename='css/walk_in_sales.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/sweet-alert.css') }}">
<script src="{{ url_for('static', filename='js/sweet-alert.js') }}"></script>
<script src="{{ url_for('static', filename='js/walk_in_sales.js') }}"></script>
{% endblock %}
